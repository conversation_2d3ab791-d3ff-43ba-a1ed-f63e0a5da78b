{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\components\\productSpecs.vue?vue&type=template&id=f7b5f20a", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\components\\productSpecs.vue", "mtime": 1750415584837}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    [\n      _c(\n        \"el-row\",\n        [\n          _c(\n            \"el-col\",\n            { attrs: { span: 24 } },\n            [\n              _c(\n                \"el-form-item\",\n                { attrs: { label: _vm.$t(\"规格类型：\"), props: \"spec_type\" } },\n                [\n                  _c(\n                    \"el-radio-group\",\n                    {\n                      model: {\n                        value: _vm.formValidate.spec_type,\n                        callback: function ($$v) {\n                          _vm.$set(_vm.formValidate, \"spec_type\", $$v)\n                        },\n                        expression: \"formValidate.spec_type\",\n                      },\n                    },\n                    [\n                      _c(\n                        \"el-radio\",\n                        { staticClass: \"radio\", attrs: { label: 0 } },\n                        [_vm._v(_vm._s(_vm.$t(\"单规格\")))]\n                      ),\n                      _vm._v(\" \"),\n                      _c(\"el-radio\", { attrs: { label: 1 } }, [\n                        _vm._v(_vm._s(_vm.$t(\"多规格\"))),\n                      ]),\n                      _vm._v(\" \"),\n                      _c(\"el-radio\", { attrs: { label: 2 } }, [\n                        _vm._v(_vm._s(_vm.$t(\"服务包模式\"))),\n                      ]),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _vm.formValidate.spec_type == 1 && _vm.ruleList.length > 0\n                    ? _c(\n                        \"el-dropdown\",\n                        {\n                          staticClass: \"ml20\",\n                          attrs: { trigger: \"hover\" },\n                          on: { command: _vm.confirm },\n                        },\n                        [\n                          _c(\"span\", { staticClass: \"el-dropdown-link\" }, [\n                            _vm._v(_vm._s(_vm.$t(\"选择规格模板\"))),\n                            _c(\"i\", {\n                              staticClass: \"el-icon-arrow-down el-icon--right\",\n                            }),\n                          ]),\n                          _vm._v(\" \"),\n                          _c(\n                            \"el-dropdown-menu\",\n                            { attrs: { slot: \"dropdown\" }, slot: \"dropdown\" },\n                            [\n                              _c(\n                                \"el-scrollbar\",\n                                {\n                                  staticStyle: {\n                                    \"max-height\": \"300px\",\n                                    \"overflow-y\": \"scroll\",\n                                  },\n                                },\n                                _vm._l(_vm.ruleList, function (item, index) {\n                                  return _c(\n                                    \"el-dropdown-item\",\n                                    {\n                                      key: index,\n                                      attrs: { command: item.attr_template_id },\n                                    },\n                                    [\n                                      _vm._v(\n                                        \"\\n                \" +\n                                          _vm._s(item.template_name) +\n                                          \"\\n              \"\n                                      ),\n                                    ]\n                                  )\n                                }),\n                                1\n                              ),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _vm.formValidate.spec_type === 2\n            ? _c(\n                \"el-col\",\n                { staticClass: \"noForm\", attrs: { span: 24 } },\n                [\n                  _c(\n                    \"el-form-item\",\n                    { attrs: { label: _vm.$t(\"服务包配置：\"), required: \"\" } },\n                    [\n                      _c(\"div\", { staticClass: \"service-packages\" }, [\n                        _c(\n                          \"div\",\n                          { staticClass: \"package-card basic-package\" },\n                          [\n                            _c(\n                              \"div\",\n                              { staticClass: \"package-header\" },\n                              [\n                                _c(\"h3\", [\n                                  _vm._v(_vm._s(_vm.$t(\"基础包 (Basic)\"))),\n                                ]),\n                                _vm._v(\" \"),\n                                _c(\"el-switch\", {\n                                  attrs: {\n                                    \"active-text\": _vm.$t(\"启用\"),\n                                    \"inactive-text\": _vm.$t(\"禁用\"),\n                                  },\n                                  on: {\n                                    change: function ($event) {\n                                      return _vm.onPackageToggle(\"basic\")\n                                    },\n                                  },\n                                  model: {\n                                    value: _vm.packageConfig.basic.enabled,\n                                    callback: function ($$v) {\n                                      _vm.$set(\n                                        _vm.packageConfig.basic,\n                                        \"enabled\",\n                                        $$v\n                                      )\n                                    },\n                                    expression: \"packageConfig.basic.enabled\",\n                                  },\n                                }),\n                              ],\n                              1\n                            ),\n                            _vm._v(\" \"),\n                            _vm.packageConfig.basic.enabled\n                              ? _c(\n                                  \"div\",\n                                  { staticClass: \"package-content\" },\n                                  [\n                                    _c(\n                                      \"el-form\",\n                                      {\n                                        attrs: {\n                                          model: _vm.packageConfig.basic,\n                                          \"label-width\": \"100px\",\n                                          size: \"small\",\n                                        },\n                                      },\n                                      [\n                                        _c(\n                                          \"el-form-item\",\n                                          {\n                                            attrs: { label: _vm.$t(\"包名称\") },\n                                          },\n                                          [\n                                            _c(\"el-input\", {\n                                              attrs: {\n                                                placeholder:\n                                                  _vm.$t(\"请输入包名称\"),\n                                              },\n                                              model: {\n                                                value:\n                                                  _vm.packageConfig.basic.name,\n                                                callback: function ($$v) {\n                                                  _vm.$set(\n                                                    _vm.packageConfig.basic,\n                                                    \"name\",\n                                                    $$v\n                                                  )\n                                                },\n                                                expression:\n                                                  \"packageConfig.basic.name\",\n                                              },\n                                            }),\n                                          ],\n                                          1\n                                        ),\n                                        _vm._v(\" \"),\n                                        _c(\n                                          \"el-form-item\",\n                                          {\n                                            attrs: { label: _vm.$t(\"包描述\") },\n                                          },\n                                          [\n                                            _c(\"el-input\", {\n                                              attrs: {\n                                                type: \"textarea\",\n                                                placeholder:\n                                                  _vm.$t(\"请输入包描述\"),\n                                                rows: 3,\n                                              },\n                                              model: {\n                                                value:\n                                                  _vm.packageConfig.basic\n                                                    .description,\n                                                callback: function ($$v) {\n                                                  _vm.$set(\n                                                    _vm.packageConfig.basic,\n                                                    \"description\",\n                                                    $$v\n                                                  )\n                                                },\n                                                expression:\n                                                  \"packageConfig.basic.description\",\n                                              },\n                                            }),\n                                          ],\n                                          1\n                                        ),\n                                        _vm._v(\" \"),\n                                        _c(\n                                          \"el-form-item\",\n                                          {\n                                            attrs: {\n                                              label: _vm.$t(\"交付时间\"),\n                                            },\n                                          },\n                                          [\n                                            _c(\n                                              \"el-select\",\n                                              {\n                                                attrs: {\n                                                  placeholder:\n                                                    _vm.$t(\"请选择交付时间\"),\n                                                },\n                                                model: {\n                                                  value:\n                                                    _vm.packageConfig.basic\n                                                      .deliveryTime,\n                                                  callback: function ($$v) {\n                                                    _vm.$set(\n                                                      _vm.packageConfig.basic,\n                                                      \"deliveryTime\",\n                                                      $$v\n                                                    )\n                                                  },\n                                                  expression:\n                                                    \"packageConfig.basic.deliveryTime\",\n                                                },\n                                              },\n                                              [\n                                                _c(\"el-option\", {\n                                                  attrs: {\n                                                    label: \"1天\",\n                                                    value: \"1\",\n                                                  },\n                                                }),\n                                                _vm._v(\" \"),\n                                                _c(\"el-option\", {\n                                                  attrs: {\n                                                    label: \"2天\",\n                                                    value: \"2\",\n                                                  },\n                                                }),\n                                                _vm._v(\" \"),\n                                                _c(\"el-option\", {\n                                                  attrs: {\n                                                    label: \"3天\",\n                                                    value: \"3\",\n                                                  },\n                                                }),\n                                                _vm._v(\" \"),\n                                                _c(\"el-option\", {\n                                                  attrs: {\n                                                    label: \"5天\",\n                                                    value: \"5\",\n                                                  },\n                                                }),\n                                                _vm._v(\" \"),\n                                                _c(\"el-option\", {\n                                                  attrs: {\n                                                    label: \"7天\",\n                                                    value: \"7\",\n                                                  },\n                                                }),\n                                                _vm._v(\" \"),\n                                                _c(\"el-option\", {\n                                                  attrs: {\n                                                    label: \"10天\",\n                                                    value: \"10\",\n                                                  },\n                                                }),\n                                                _vm._v(\" \"),\n                                                _c(\"el-option\", {\n                                                  attrs: {\n                                                    label: \"15天\",\n                                                    value: \"15\",\n                                                  },\n                                                }),\n                                                _vm._v(\" \"),\n                                                _c(\"el-option\", {\n                                                  attrs: {\n                                                    label: \"30天\",\n                                                    value: \"30\",\n                                                  },\n                                                }),\n                                              ],\n                                              1\n                                            ),\n                                          ],\n                                          1\n                                        ),\n                                        _vm._v(\" \"),\n                                        _c(\n                                          \"el-form-item\",\n                                          {\n                                            attrs: {\n                                              label: _vm.$t(\"修改次数\"),\n                                            },\n                                          },\n                                          [\n                                            _c(\n                                              \"el-select\",\n                                              {\n                                                attrs: {\n                                                  placeholder:\n                                                    _vm.$t(\"请选择修改次数\"),\n                                                },\n                                                model: {\n                                                  value:\n                                                    _vm.packageConfig.basic\n                                                      .revisions,\n                                                  callback: function ($$v) {\n                                                    _vm.$set(\n                                                      _vm.packageConfig.basic,\n                                                      \"revisions\",\n                                                      $$v\n                                                    )\n                                                  },\n                                                  expression:\n                                                    \"packageConfig.basic.revisions\",\n                                                },\n                                              },\n                                              [\n                                                _c(\"el-option\", {\n                                                  attrs: {\n                                                    label: \"不包含修改\",\n                                                    value: \"0\",\n                                                  },\n                                                }),\n                                                _vm._v(\" \"),\n                                                _c(\"el-option\", {\n                                                  attrs: {\n                                                    label: \"1次修改\",\n                                                    value: \"1\",\n                                                  },\n                                                }),\n                                                _vm._v(\" \"),\n                                                _c(\"el-option\", {\n                                                  attrs: {\n                                                    label: \"2次修改\",\n                                                    value: \"2\",\n                                                  },\n                                                }),\n                                                _vm._v(\" \"),\n                                                _c(\"el-option\", {\n                                                  attrs: {\n                                                    label: \"3次修改\",\n                                                    value: \"3\",\n                                                  },\n                                                }),\n                                                _vm._v(\" \"),\n                                                _c(\"el-option\", {\n                                                  attrs: {\n                                                    label: \"5次修改\",\n                                                    value: \"5\",\n                                                  },\n                                                }),\n                                                _vm._v(\" \"),\n                                                _c(\"el-option\", {\n                                                  attrs: {\n                                                    label: \"无限修改\",\n                                                    value: \"unlimited\",\n                                                  },\n                                                }),\n                                              ],\n                                              1\n                                            ),\n                                          ],\n                                          1\n                                        ),\n                                        _vm._v(\" \"),\n                                        _c(\n                                          \"el-form-item\",\n                                          { attrs: { label: _vm.$t(\"价格\") } },\n                                          [\n                                            _c(\"el-input-number\", {\n                                              attrs: {\n                                                min: 0,\n                                                precision: 2,\n                                                \"controls-position\": \"right\",\n                                              },\n                                              model: {\n                                                value:\n                                                  _vm.packageConfig.basic.price,\n                                                callback: function ($$v) {\n                                                  _vm.$set(\n                                                    _vm.packageConfig.basic,\n                                                    \"price\",\n                                                    $$v\n                                                  )\n                                                },\n                                                expression:\n                                                  \"packageConfig.basic.price\",\n                                              },\n                                            }),\n                                          ],\n                                          1\n                                        ),\n                                      ],\n                                      1\n                                    ),\n                                  ],\n                                  1\n                                )\n                              : _vm._e(),\n                          ]\n                        ),\n                        _vm._v(\" \"),\n                        _vm.packageConfig.basic.enabled\n                          ? _c(\n                              \"div\",\n                              { staticClass: \"package-actions\" },\n                              [\n                                !_vm.showAdvancedPackages\n                                  ? _c(\n                                      \"el-button\",\n                                      {\n                                        staticClass: \"create-packages-btn\",\n                                        attrs: { type: \"primary\" },\n                                        on: {\n                                          click: _vm.createAdvancedPackages,\n                                        },\n                                      },\n                                      [\n                                        _vm._v(\n                                          \"\\n              \" +\n                                            _vm._s(_vm.$t(\"Create Packages\")) +\n                                            \"\\n            \"\n                                        ),\n                                      ]\n                                    )\n                                  : _vm._e(),\n                                _vm._v(\" \"),\n                                _vm.showAdvancedPackages\n                                  ? _c(\n                                      \"div\",\n                                      { staticClass: \"advanced-packages\" },\n                                      [\n                                        _c(\n                                          \"div\",\n                                          {\n                                            staticClass:\n                                              \"package-card standard-package\",\n                                          },\n                                          [\n                                            _c(\n                                              \"div\",\n                                              { staticClass: \"package-header\" },\n                                              [\n                                                _c(\"h3\", [\n                                                  _vm._v(\n                                                    _vm._s(\n                                                      _vm.$t(\n                                                        \"标准包 (Standard)\"\n                                                      )\n                                                    )\n                                                  ),\n                                                ]),\n                                                _vm._v(\" \"),\n                                                _c(\"el-switch\", {\n                                                  attrs: {\n                                                    \"active-text\":\n                                                      _vm.$t(\"启用\"),\n                                                    \"inactive-text\":\n                                                      _vm.$t(\"禁用\"),\n                                                  },\n                                                  model: {\n                                                    value:\n                                                      _vm.packageConfig.standard\n                                                        .enabled,\n                                                    callback: function ($$v) {\n                                                      _vm.$set(\n                                                        _vm.packageConfig\n                                                          .standard,\n                                                        \"enabled\",\n                                                        $$v\n                                                      )\n                                                    },\n                                                    expression:\n                                                      \"packageConfig.standard.enabled\",\n                                                  },\n                                                }),\n                                              ],\n                                              1\n                                            ),\n                                            _vm._v(\" \"),\n                                            _vm.packageConfig.standard.enabled\n                                              ? _c(\n                                                  \"div\",\n                                                  {\n                                                    staticClass:\n                                                      \"package-content\",\n                                                  },\n                                                  [\n                                                    _c(\n                                                      \"el-form\",\n                                                      {\n                                                        attrs: {\n                                                          model:\n                                                            _vm.packageConfig\n                                                              .standard,\n                                                          \"label-width\":\n                                                            \"100px\",\n                                                          size: \"small\",\n                                                        },\n                                                      },\n                                                      [\n                                                        _c(\n                                                          \"el-form-item\",\n                                                          {\n                                                            attrs: {\n                                                              label:\n                                                                _vm.$t(\n                                                                  \"包名称\"\n                                                                ),\n                                                            },\n                                                          },\n                                                          [\n                                                            _c(\"el-input\", {\n                                                              attrs: {\n                                                                placeholder:\n                                                                  _vm.$t(\n                                                                    \"请输入包名称\"\n                                                                  ),\n                                                              },\n                                                              model: {\n                                                                value:\n                                                                  _vm\n                                                                    .packageConfig\n                                                                    .standard\n                                                                    .name,\n                                                                callback:\n                                                                  function (\n                                                                    $$v\n                                                                  ) {\n                                                                    _vm.$set(\n                                                                      _vm\n                                                                        .packageConfig\n                                                                        .standard,\n                                                                      \"name\",\n                                                                      $$v\n                                                                    )\n                                                                  },\n                                                                expression:\n                                                                  \"packageConfig.standard.name\",\n                                                              },\n                                                            }),\n                                                          ],\n                                                          1\n                                                        ),\n                                                        _vm._v(\" \"),\n                                                        _c(\n                                                          \"el-form-item\",\n                                                          {\n                                                            attrs: {\n                                                              label:\n                                                                _vm.$t(\n                                                                  \"包描述\"\n                                                                ),\n                                                            },\n                                                          },\n                                                          [\n                                                            _c(\"el-input\", {\n                                                              attrs: {\n                                                                type: \"textarea\",\n                                                                placeholder:\n                                                                  _vm.$t(\n                                                                    \"请输入包描述\"\n                                                                  ),\n                                                                rows: 3,\n                                                              },\n                                                              model: {\n                                                                value:\n                                                                  _vm\n                                                                    .packageConfig\n                                                                    .standard\n                                                                    .description,\n                                                                callback:\n                                                                  function (\n                                                                    $$v\n                                                                  ) {\n                                                                    _vm.$set(\n                                                                      _vm\n                                                                        .packageConfig\n                                                                        .standard,\n                                                                      \"description\",\n                                                                      $$v\n                                                                    )\n                                                                  },\n                                                                expression:\n                                                                  \"packageConfig.standard.description\",\n                                                              },\n                                                            }),\n                                                          ],\n                                                          1\n                                                        ),\n                                                        _vm._v(\" \"),\n                                                        _c(\n                                                          \"el-form-item\",\n                                                          {\n                                                            attrs: {\n                                                              label:\n                                                                _vm.$t(\n                                                                  \"交付时间\"\n                                                                ),\n                                                            },\n                                                          },\n                                                          [\n                                                            _c(\n                                                              \"el-select\",\n                                                              {\n                                                                attrs: {\n                                                                  placeholder:\n                                                                    _vm.$t(\n                                                                      \"请选择交付时间\"\n                                                                    ),\n                                                                },\n                                                                model: {\n                                                                  value:\n                                                                    _vm\n                                                                      .packageConfig\n                                                                      .standard\n                                                                      .deliveryTime,\n                                                                  callback:\n                                                                    function (\n                                                                      $$v\n                                                                    ) {\n                                                                      _vm.$set(\n                                                                        _vm\n                                                                          .packageConfig\n                                                                          .standard,\n                                                                        \"deliveryTime\",\n                                                                        $$v\n                                                                      )\n                                                                    },\n                                                                  expression:\n                                                                    \"packageConfig.standard.deliveryTime\",\n                                                                },\n                                                              },\n                                                              [\n                                                                _c(\n                                                                  \"el-option\",\n                                                                  {\n                                                                    attrs: {\n                                                                      label:\n                                                                        \"1天\",\n                                                                      value:\n                                                                        \"1\",\n                                                                    },\n                                                                  }\n                                                                ),\n                                                                _vm._v(\" \"),\n                                                                _c(\n                                                                  \"el-option\",\n                                                                  {\n                                                                    attrs: {\n                                                                      label:\n                                                                        \"2天\",\n                                                                      value:\n                                                                        \"2\",\n                                                                    },\n                                                                  }\n                                                                ),\n                                                                _vm._v(\" \"),\n                                                                _c(\n                                                                  \"el-option\",\n                                                                  {\n                                                                    attrs: {\n                                                                      label:\n                                                                        \"3天\",\n                                                                      value:\n                                                                        \"3\",\n                                                                    },\n                                                                  }\n                                                                ),\n                                                                _vm._v(\" \"),\n                                                                _c(\n                                                                  \"el-option\",\n                                                                  {\n                                                                    attrs: {\n                                                                      label:\n                                                                        \"5天\",\n                                                                      value:\n                                                                        \"5\",\n                                                                    },\n                                                                  }\n                                                                ),\n                                                                _vm._v(\" \"),\n                                                                _c(\n                                                                  \"el-option\",\n                                                                  {\n                                                                    attrs: {\n                                                                      label:\n                                                                        \"7天\",\n                                                                      value:\n                                                                        \"7\",\n                                                                    },\n                                                                  }\n                                                                ),\n                                                                _vm._v(\" \"),\n                                                                _c(\n                                                                  \"el-option\",\n                                                                  {\n                                                                    attrs: {\n                                                                      label:\n                                                                        \"10天\",\n                                                                      value:\n                                                                        \"10\",\n                                                                    },\n                                                                  }\n                                                                ),\n                                                                _vm._v(\" \"),\n                                                                _c(\n                                                                  \"el-option\",\n                                                                  {\n                                                                    attrs: {\n                                                                      label:\n                                                                        \"15天\",\n                                                                      value:\n                                                                        \"15\",\n                                                                    },\n                                                                  }\n                                                                ),\n                                                                _vm._v(\" \"),\n                                                                _c(\n                                                                  \"el-option\",\n                                                                  {\n                                                                    attrs: {\n                                                                      label:\n                                                                        \"30天\",\n                                                                      value:\n                                                                        \"30\",\n                                                                    },\n                                                                  }\n                                                                ),\n                                                              ],\n                                                              1\n                                                            ),\n                                                          ],\n                                                          1\n                                                        ),\n                                                        _vm._v(\" \"),\n                                                        _c(\n                                                          \"el-form-item\",\n                                                          {\n                                                            attrs: {\n                                                              label:\n                                                                _vm.$t(\n                                                                  \"修改次数\"\n                                                                ),\n                                                            },\n                                                          },\n                                                          [\n                                                            _c(\n                                                              \"el-select\",\n                                                              {\n                                                                attrs: {\n                                                                  placeholder:\n                                                                    _vm.$t(\n                                                                      \"请选择修改次数\"\n                                                                    ),\n                                                                },\n                                                                model: {\n                                                                  value:\n                                                                    _vm\n                                                                      .packageConfig\n                                                                      .standard\n                                                                      .revisions,\n                                                                  callback:\n                                                                    function (\n                                                                      $$v\n                                                                    ) {\n                                                                      _vm.$set(\n                                                                        _vm\n                                                                          .packageConfig\n                                                                          .standard,\n                                                                        \"revisions\",\n                                                                        $$v\n                                                                      )\n                                                                    },\n                                                                  expression:\n                                                                    \"packageConfig.standard.revisions\",\n                                                                },\n                                                              },\n                                                              [\n                                                                _c(\n                                                                  \"el-option\",\n                                                                  {\n                                                                    attrs: {\n                                                                      label:\n                                                                        \"不包含修改\",\n                                                                      value:\n                                                                        \"0\",\n                                                                    },\n                                                                  }\n                                                                ),\n                                                                _vm._v(\" \"),\n                                                                _c(\n                                                                  \"el-option\",\n                                                                  {\n                                                                    attrs: {\n                                                                      label:\n                                                                        \"1次修改\",\n                                                                      value:\n                                                                        \"1\",\n                                                                    },\n                                                                  }\n                                                                ),\n                                                                _vm._v(\" \"),\n                                                                _c(\n                                                                  \"el-option\",\n                                                                  {\n                                                                    attrs: {\n                                                                      label:\n                                                                        \"2次修改\",\n                                                                      value:\n                                                                        \"2\",\n                                                                    },\n                                                                  }\n                                                                ),\n                                                                _vm._v(\" \"),\n                                                                _c(\n                                                                  \"el-option\",\n                                                                  {\n                                                                    attrs: {\n                                                                      label:\n                                                                        \"3次修改\",\n                                                                      value:\n                                                                        \"3\",\n                                                                    },\n                                                                  }\n                                                                ),\n                                                                _vm._v(\" \"),\n                                                                _c(\n                                                                  \"el-option\",\n                                                                  {\n                                                                    attrs: {\n                                                                      label:\n                                                                        \"5次修改\",\n                                                                      value:\n                                                                        \"5\",\n                                                                    },\n                                                                  }\n                                                                ),\n                                                                _vm._v(\" \"),\n                                                                _c(\n                                                                  \"el-option\",\n                                                                  {\n                                                                    attrs: {\n                                                                      label:\n                                                                        \"无限修改\",\n                                                                      value:\n                                                                        \"unlimited\",\n                                                                    },\n                                                                  }\n                                                                ),\n                                                              ],\n                                                              1\n                                                            ),\n                                                          ],\n                                                          1\n                                                        ),\n                                                        _vm._v(\" \"),\n                                                        _c(\n                                                          \"el-form-item\",\n                                                          {\n                                                            attrs: {\n                                                              label:\n                                                                _vm.$t(\"价格\"),\n                                                            },\n                                                          },\n                                                          [\n                                                            _c(\n                                                              \"el-input-number\",\n                                                              {\n                                                                attrs: {\n                                                                  min: 0,\n                                                                  precision: 2,\n                                                                  \"controls-position\":\n                                                                    \"right\",\n                                                                },\n                                                                model: {\n                                                                  value:\n                                                                    _vm\n                                                                      .packageConfig\n                                                                      .standard\n                                                                      .price,\n                                                                  callback:\n                                                                    function (\n                                                                      $$v\n                                                                    ) {\n                                                                      _vm.$set(\n                                                                        _vm\n                                                                          .packageConfig\n                                                                          .standard,\n                                                                        \"price\",\n                                                                        $$v\n                                                                      )\n                                                                    },\n                                                                  expression:\n                                                                    \"packageConfig.standard.price\",\n                                                                },\n                                                              }\n                                                            ),\n                                                          ],\n                                                          1\n                                                        ),\n                                                      ],\n                                                      1\n                                                    ),\n                                                  ],\n                                                  1\n                                                )\n                                              : _vm._e(),\n                                          ]\n                                        ),\n                                        _vm._v(\" \"),\n                                        _c(\n                                          \"div\",\n                                          {\n                                            staticClass:\n                                              \"package-card premium-package\",\n                                          },\n                                          [\n                                            _c(\n                                              \"div\",\n                                              { staticClass: \"package-header\" },\n                                              [\n                                                _c(\"h3\", [\n                                                  _vm._v(\n                                                    _vm._s(\n                                                      _vm.$t(\"高级包 (Premium)\")\n                                                    )\n                                                  ),\n                                                ]),\n                                                _vm._v(\" \"),\n                                                _c(\"el-switch\", {\n                                                  attrs: {\n                                                    \"active-text\":\n                                                      _vm.$t(\"启用\"),\n                                                    \"inactive-text\":\n                                                      _vm.$t(\"禁用\"),\n                                                  },\n                                                  model: {\n                                                    value:\n                                                      _vm.packageConfig.premium\n                                                        .enabled,\n                                                    callback: function ($$v) {\n                                                      _vm.$set(\n                                                        _vm.packageConfig\n                                                          .premium,\n                                                        \"enabled\",\n                                                        $$v\n                                                      )\n                                                    },\n                                                    expression:\n                                                      \"packageConfig.premium.enabled\",\n                                                  },\n                                                }),\n                                              ],\n                                              1\n                                            ),\n                                            _vm._v(\" \"),\n                                            _vm.packageConfig.premium.enabled\n                                              ? _c(\n                                                  \"div\",\n                                                  {\n                                                    staticClass:\n                                                      \"package-content\",\n                                                  },\n                                                  [\n                                                    _c(\n                                                      \"el-form\",\n                                                      {\n                                                        attrs: {\n                                                          model:\n                                                            _vm.packageConfig\n                                                              .premium,\n                                                          \"label-width\":\n                                                            \"100px\",\n                                                          size: \"small\",\n                                                        },\n                                                      },\n                                                      [\n                                                        _c(\n                                                          \"el-form-item\",\n                                                          {\n                                                            attrs: {\n                                                              label:\n                                                                _vm.$t(\n                                                                  \"包名称\"\n                                                                ),\n                                                            },\n                                                          },\n                                                          [\n                                                            _c(\"el-input\", {\n                                                              attrs: {\n                                                                placeholder:\n                                                                  _vm.$t(\n                                                                    \"请输入包名称\"\n                                                                  ),\n                                                              },\n                                                              model: {\n                                                                value:\n                                                                  _vm\n                                                                    .packageConfig\n                                                                    .premium\n                                                                    .name,\n                                                                callback:\n                                                                  function (\n                                                                    $$v\n                                                                  ) {\n                                                                    _vm.$set(\n                                                                      _vm\n                                                                        .packageConfig\n                                                                        .premium,\n                                                                      \"name\",\n                                                                      $$v\n                                                                    )\n                                                                  },\n                                                                expression:\n                                                                  \"packageConfig.premium.name\",\n                                                              },\n                                                            }),\n                                                          ],\n                                                          1\n                                                        ),\n                                                        _vm._v(\" \"),\n                                                        _c(\n                                                          \"el-form-item\",\n                                                          {\n                                                            attrs: {\n                                                              label:\n                                                                _vm.$t(\n                                                                  \"包描述\"\n                                                                ),\n                                                            },\n                                                          },\n                                                          [\n                                                            _c(\"el-input\", {\n                                                              attrs: {\n                                                                type: \"textarea\",\n                                                                placeholder:\n                                                                  _vm.$t(\n                                                                    \"请输入包描述\"\n                                                                  ),\n                                                                rows: 3,\n                                                              },\n                                                              model: {\n                                                                value:\n                                                                  _vm\n                                                                    .packageConfig\n                                                                    .premium\n                                                                    .description,\n                                                                callback:\n                                                                  function (\n                                                                    $$v\n                                                                  ) {\n                                                                    _vm.$set(\n                                                                      _vm\n                                                                        .packageConfig\n                                                                        .premium,\n                                                                      \"description\",\n                                                                      $$v\n                                                                    )\n                                                                  },\n                                                                expression:\n                                                                  \"packageConfig.premium.description\",\n                                                              },\n                                                            }),\n                                                          ],\n                                                          1\n                                                        ),\n                                                        _vm._v(\" \"),\n                                                        _c(\n                                                          \"el-form-item\",\n                                                          {\n                                                            attrs: {\n                                                              label:\n                                                                _vm.$t(\n                                                                  \"交付时间\"\n                                                                ),\n                                                            },\n                                                          },\n                                                          [\n                                                            _c(\n                                                              \"el-select\",\n                                                              {\n                                                                attrs: {\n                                                                  placeholder:\n                                                                    _vm.$t(\n                                                                      \"请选择交付时间\"\n                                                                    ),\n                                                                },\n                                                                model: {\n                                                                  value:\n                                                                    _vm\n                                                                      .packageConfig\n                                                                      .premium\n                                                                      .deliveryTime,\n                                                                  callback:\n                                                                    function (\n                                                                      $$v\n                                                                    ) {\n                                                                      _vm.$set(\n                                                                        _vm\n                                                                          .packageConfig\n                                                                          .premium,\n                                                                        \"deliveryTime\",\n                                                                        $$v\n                                                                      )\n                                                                    },\n                                                                  expression:\n                                                                    \"packageConfig.premium.deliveryTime\",\n                                                                },\n                                                              },\n                                                              [\n                                                                _c(\n                                                                  \"el-option\",\n                                                                  {\n                                                                    attrs: {\n                                                                      label:\n                                                                        \"1天\",\n                                                                      value:\n                                                                        \"1\",\n                                                                    },\n                                                                  }\n                                                                ),\n                                                                _vm._v(\" \"),\n                                                                _c(\n                                                                  \"el-option\",\n                                                                  {\n                                                                    attrs: {\n                                                                      label:\n                                                                        \"2天\",\n                                                                      value:\n                                                                        \"2\",\n                                                                    },\n                                                                  }\n                                                                ),\n                                                                _vm._v(\" \"),\n                                                                _c(\n                                                                  \"el-option\",\n                                                                  {\n                                                                    attrs: {\n                                                                      label:\n                                                                        \"3天\",\n                                                                      value:\n                                                                        \"3\",\n                                                                    },\n                                                                  }\n                                                                ),\n                                                                _vm._v(\" \"),\n                                                                _c(\n                                                                  \"el-option\",\n                                                                  {\n                                                                    attrs: {\n                                                                      label:\n                                                                        \"5天\",\n                                                                      value:\n                                                                        \"5\",\n                                                                    },\n                                                                  }\n                                                                ),\n                                                                _vm._v(\" \"),\n                                                                _c(\n                                                                  \"el-option\",\n                                                                  {\n                                                                    attrs: {\n                                                                      label:\n                                                                        \"7天\",\n                                                                      value:\n                                                                        \"7\",\n                                                                    },\n                                                                  }\n                                                                ),\n                                                                _vm._v(\" \"),\n                                                                _c(\n                                                                  \"el-option\",\n                                                                  {\n                                                                    attrs: {\n                                                                      label:\n                                                                        \"10天\",\n                                                                      value:\n                                                                        \"10\",\n                                                                    },\n                                                                  }\n                                                                ),\n                                                                _vm._v(\" \"),\n                                                                _c(\n                                                                  \"el-option\",\n                                                                  {\n                                                                    attrs: {\n                                                                      label:\n                                                                        \"15天\",\n                                                                      value:\n                                                                        \"15\",\n                                                                    },\n                                                                  }\n                                                                ),\n                                                                _vm._v(\" \"),\n                                                                _c(\n                                                                  \"el-option\",\n                                                                  {\n                                                                    attrs: {\n                                                                      label:\n                                                                        \"30天\",\n                                                                      value:\n                                                                        \"30\",\n                                                                    },\n                                                                  }\n                                                                ),\n                                                              ],\n                                                              1\n                                                            ),\n                                                          ],\n                                                          1\n                                                        ),\n                                                        _vm._v(\" \"),\n                                                        _c(\n                                                          \"el-form-item\",\n                                                          {\n                                                            attrs: {\n                                                              label:\n                                                                _vm.$t(\n                                                                  \"修改次数\"\n                                                                ),\n                                                            },\n                                                          },\n                                                          [\n                                                            _c(\n                                                              \"el-select\",\n                                                              {\n                                                                attrs: {\n                                                                  placeholder:\n                                                                    _vm.$t(\n                                                                      \"请选择修改次数\"\n                                                                    ),\n                                                                },\n                                                                model: {\n                                                                  value:\n                                                                    _vm\n                                                                      .packageConfig\n                                                                      .premium\n                                                                      .revisions,\n                                                                  callback:\n                                                                    function (\n                                                                      $$v\n                                                                    ) {\n                                                                      _vm.$set(\n                                                                        _vm\n                                                                          .packageConfig\n                                                                          .premium,\n                                                                        \"revisions\",\n                                                                        $$v\n                                                                      )\n                                                                    },\n                                                                  expression:\n                                                                    \"packageConfig.premium.revisions\",\n                                                                },\n                                                              },\n                                                              [\n                                                                _c(\n                                                                  \"el-option\",\n                                                                  {\n                                                                    attrs: {\n                                                                      label:\n                                                                        \"不包含修改\",\n                                                                      value:\n                                                                        \"0\",\n                                                                    },\n                                                                  }\n                                                                ),\n                                                                _vm._v(\" \"),\n                                                                _c(\n                                                                  \"el-option\",\n                                                                  {\n                                                                    attrs: {\n                                                                      label:\n                                                                        \"1次修改\",\n                                                                      value:\n                                                                        \"1\",\n                                                                    },\n                                                                  }\n                                                                ),\n                                                                _vm._v(\" \"),\n                                                                _c(\n                                                                  \"el-option\",\n                                                                  {\n                                                                    attrs: {\n                                                                      label:\n                                                                        \"2次修改\",\n                                                                      value:\n                                                                        \"2\",\n                                                                    },\n                                                                  }\n                                                                ),\n                                                                _vm._v(\" \"),\n                                                                _c(\n                                                                  \"el-option\",\n                                                                  {\n                                                                    attrs: {\n                                                                      label:\n                                                                        \"3次修改\",\n                                                                      value:\n                                                                        \"3\",\n                                                                    },\n                                                                  }\n                                                                ),\n                                                                _vm._v(\" \"),\n                                                                _c(\n                                                                  \"el-option\",\n                                                                  {\n                                                                    attrs: {\n                                                                      label:\n                                                                        \"5次修改\",\n                                                                      value:\n                                                                        \"5\",\n                                                                    },\n                                                                  }\n                                                                ),\n                                                                _vm._v(\" \"),\n                                                                _c(\n                                                                  \"el-option\",\n                                                                  {\n                                                                    attrs: {\n                                                                      label:\n                                                                        \"无限修改\",\n                                                                      value:\n                                                                        \"unlimited\",\n                                                                    },\n                                                                  }\n                                                                ),\n                                                              ],\n                                                              1\n                                                            ),\n                                                          ],\n                                                          1\n                                                        ),\n                                                        _vm._v(\" \"),\n                                                        _c(\n                                                          \"el-form-item\",\n                                                          {\n                                                            attrs: {\n                                                              label:\n                                                                _vm.$t(\"价格\"),\n                                                            },\n                                                          },\n                                                          [\n                                                            _c(\n                                                              \"el-input-number\",\n                                                              {\n                                                                attrs: {\n                                                                  min: 0,\n                                                                  precision: 2,\n                                                                  \"controls-position\":\n                                                                    \"right\",\n                                                                },\n                                                                model: {\n                                                                  value:\n                                                                    _vm\n                                                                      .packageConfig\n                                                                      .premium\n                                                                      .price,\n                                                                  callback:\n                                                                    function (\n                                                                      $$v\n                                                                    ) {\n                                                                      _vm.$set(\n                                                                        _vm\n                                                                          .packageConfig\n                                                                          .premium,\n                                                                        \"price\",\n                                                                        $$v\n                                                                      )\n                                                                    },\n                                                                  expression:\n                                                                    \"packageConfig.premium.price\",\n                                                                },\n                                                              }\n                                                            ),\n                                                          ],\n                                                          1\n                                                        ),\n                                                      ],\n                                                      1\n                                                    ),\n                                                  ],\n                                                  1\n                                                )\n                                              : _vm._e(),\n                                          ]\n                                        ),\n                                      ]\n                                    )\n                                  : _vm._e(),\n                              ],\n                              1\n                            )\n                          : _vm._e(),\n                        _vm._v(\" \"),\n                        _vm.showAdvancedPackages\n                          ? _c(\"div\", { staticClass: \"extra-services\" }, [\n                              _c(\"h3\", [\n                                _vm._v(\n                                  _vm._s(_vm.$t(\"额外服务 (Extra Services)\"))\n                                ),\n                              ]),\n                              _vm._v(\" \"),\n                              _c(\n                                \"div\",\n                                { staticClass: \"extra-service-item\" },\n                                [\n                                  _c(\n                                    \"el-checkbox\",\n                                    {\n                                      model: {\n                                        value:\n                                          _vm.extraServices.fastDelivery\n                                            .enabled,\n                                        callback: function ($$v) {\n                                          _vm.$set(\n                                            _vm.extraServices.fastDelivery,\n                                            \"enabled\",\n                                            $$v\n                                          )\n                                        },\n                                        expression:\n                                          \"extraServices.fastDelivery.enabled\",\n                                      },\n                                    },\n                                    [\n                                      _vm._v(\n                                        \"\\n                \" +\n                                          _vm._s(_vm.$t(\"快速交付\")) +\n                                          \"\\n              \"\n                                      ),\n                                    ]\n                                  ),\n                                  _vm._v(\" \"),\n                                  _vm.extraServices.fastDelivery.enabled\n                                    ? _c(\n                                        \"div\",\n                                        { staticClass: \"extra-service-config\" },\n                                        [\n                                          _c(\n                                            \"el-form\",\n                                            {\n                                              attrs: {\n                                                model:\n                                                  _vm.extraServices\n                                                    .fastDelivery,\n                                                \"label-width\": \"100px\",\n                                                size: \"small\",\n                                                inline: \"\",\n                                              },\n                                            },\n                                            [\n                                              _c(\n                                                \"el-form-item\",\n                                                {\n                                                  attrs: {\n                                                    label: _vm.$t(\"交付时间\"),\n                                                  },\n                                                },\n                                                [\n                                                  _c(\n                                                    \"el-select\",\n                                                    {\n                                                      model: {\n                                                        value:\n                                                          _vm.extraServices\n                                                            .fastDelivery\n                                                            .deliveryTime,\n                                                        callback: function (\n                                                          $$v\n                                                        ) {\n                                                          _vm.$set(\n                                                            _vm.extraServices\n                                                              .fastDelivery,\n                                                            \"deliveryTime\",\n                                                            $$v\n                                                          )\n                                                        },\n                                                        expression:\n                                                          \"extraServices.fastDelivery.deliveryTime\",\n                                                      },\n                                                    },\n                                                    [\n                                                      _c(\"el-option\", {\n                                                        attrs: {\n                                                          label: \"24小时\",\n                                                          value: \"1\",\n                                                        },\n                                                      }),\n                                                      _vm._v(\" \"),\n                                                      _c(\"el-option\", {\n                                                        attrs: {\n                                                          label: \"2天\",\n                                                          value: \"2\",\n                                                        },\n                                                      }),\n                                                      _vm._v(\" \"),\n                                                      _c(\"el-option\", {\n                                                        attrs: {\n                                                          label: \"3天\",\n                                                          value: \"3\",\n                                                        },\n                                                      }),\n                                                    ],\n                                                    1\n                                                  ),\n                                                ],\n                                                1\n                                              ),\n                                              _vm._v(\" \"),\n                                              _c(\n                                                \"el-form-item\",\n                                                {\n                                                  attrs: {\n                                                    label: _vm.$t(\"额外费用\"),\n                                                  },\n                                                },\n                                                [\n                                                  _c(\"el-input-number\", {\n                                                    attrs: {\n                                                      min: 0,\n                                                      precision: 2,\n                                                      \"controls-position\":\n                                                        \"right\",\n                                                    },\n                                                    model: {\n                                                      value:\n                                                        _vm.extraServices\n                                                          .fastDelivery.price,\n                                                      callback: function ($$v) {\n                                                        _vm.$set(\n                                                          _vm.extraServices\n                                                            .fastDelivery,\n                                                          \"price\",\n                                                          $$v\n                                                        )\n                                                      },\n                                                      expression:\n                                                        \"extraServices.fastDelivery.price\",\n                                                    },\n                                                  }),\n                                                ],\n                                                1\n                                              ),\n                                            ],\n                                            1\n                                          ),\n                                        ],\n                                        1\n                                      )\n                                    : _vm._e(),\n                                ],\n                                1\n                              ),\n                              _vm._v(\" \"),\n                              _c(\n                                \"div\",\n                                { staticClass: \"extra-service-item\" },\n                                [\n                                  _c(\n                                    \"el-checkbox\",\n                                    {\n                                      model: {\n                                        value:\n                                          _vm.extraServices.extraRevisions\n                                            .enabled,\n                                        callback: function ($$v) {\n                                          _vm.$set(\n                                            _vm.extraServices.extraRevisions,\n                                            \"enabled\",\n                                            $$v\n                                          )\n                                        },\n                                        expression:\n                                          \"extraServices.extraRevisions.enabled\",\n                                      },\n                                    },\n                                    [\n                                      _vm._v(\n                                        \"\\n                \" +\n                                          _vm._s(_vm.$t(\"额外修改次数\")) +\n                                          \"\\n              \"\n                                      ),\n                                    ]\n                                  ),\n                                  _vm._v(\" \"),\n                                  _vm.extraServices.extraRevisions.enabled\n                                    ? _c(\n                                        \"div\",\n                                        { staticClass: \"extra-service-config\" },\n                                        [\n                                          _c(\n                                            \"el-form\",\n                                            {\n                                              attrs: {\n                                                model:\n                                                  _vm.extraServices\n                                                    .extraRevisions,\n                                                \"label-width\": \"100px\",\n                                                size: \"small\",\n                                                inline: \"\",\n                                              },\n                                            },\n                                            [\n                                              _c(\n                                                \"el-form-item\",\n                                                {\n                                                  attrs: {\n                                                    label: _vm.$t(\"修改次数\"),\n                                                  },\n                                                },\n                                                [\n                                                  _c(\n                                                    \"el-select\",\n                                                    {\n                                                      model: {\n                                                        value:\n                                                          _vm.extraServices\n                                                            .extraRevisions\n                                                            .revisions,\n                                                        callback: function (\n                                                          $$v\n                                                        ) {\n                                                          _vm.$set(\n                                                            _vm.extraServices\n                                                              .extraRevisions,\n                                                            \"revisions\",\n                                                            $$v\n                                                          )\n                                                        },\n                                                        expression:\n                                                          \"extraServices.extraRevisions.revisions\",\n                                                      },\n                                                    },\n                                                    [\n                                                      _c(\"el-option\", {\n                                                        attrs: {\n                                                          label: \"1次修改\",\n                                                          value: \"1\",\n                                                        },\n                                                      }),\n                                                      _vm._v(\" \"),\n                                                      _c(\"el-option\", {\n                                                        attrs: {\n                                                          label: \"2次修改\",\n                                                          value: \"2\",\n                                                        },\n                                                      }),\n                                                      _vm._v(\" \"),\n                                                      _c(\"el-option\", {\n                                                        attrs: {\n                                                          label: \"3次修改\",\n                                                          value: \"3\",\n                                                        },\n                                                      }),\n                                                      _vm._v(\" \"),\n                                                      _c(\"el-option\", {\n                                                        attrs: {\n                                                          label: \"5次修改\",\n                                                          value: \"5\",\n                                                        },\n                                                      }),\n                                                    ],\n                                                    1\n                                                  ),\n                                                ],\n                                                1\n                                              ),\n                                              _vm._v(\" \"),\n                                              _c(\n                                                \"el-form-item\",\n                                                {\n                                                  attrs: {\n                                                    label: _vm.$t(\"额外费用\"),\n                                                  },\n                                                },\n                                                [\n                                                  _c(\"el-input-number\", {\n                                                    attrs: {\n                                                      min: 0,\n                                                      precision: 2,\n                                                      \"controls-position\":\n                                                        \"right\",\n                                                    },\n                                                    model: {\n                                                      value:\n                                                        _vm.extraServices\n                                                          .extraRevisions.price,\n                                                      callback: function ($$v) {\n                                                        _vm.$set(\n                                                          _vm.extraServices\n                                                            .extraRevisions,\n                                                          \"price\",\n                                                          $$v\n                                                        )\n                                                      },\n                                                      expression:\n                                                        \"extraServices.extraRevisions.price\",\n                                                    },\n                                                  }),\n                                                ],\n                                                1\n                                              ),\n                                            ],\n                                            1\n                                          ),\n                                        ],\n                                        1\n                                      )\n                                    : _vm._e(),\n                                ],\n                                1\n                              ),\n                            ])\n                          : _vm._e(),\n                      ]),\n                    ]\n                  ),\n                ],\n                1\n              )\n            : _vm._e(),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}