{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\components\\productSpecs.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\components\\productSpecs.vue", "mtime": 1750417624937}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\.babelrc", "mtime": 1749087282000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\babel.config.js", "mtime": 1735790252000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"C:/Users/<USER>/Desktop/crmeb/view/merPC/mer/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nrequire(\"core-js/modules/es6.function.name\");\nrequire(\"core-js/modules/web.dom.iterable\");\nrequire(\"core-js/modules/es6.array.find\");\nrequire(\"core-js/modules/es7.array.includes\");\nrequire(\"core-js/modules/es6.string.includes\");\nvar _createForOfIteratorHelper2 = _interopRequireDefault(require(\"C:/Users/<USER>/Desktop/crmeb/view/merPC/mer/node_modules/@babel/runtime/helpers/createForOfIteratorHelper.js\"));\nvar _toConsumableArray2 = _interopRequireDefault(require(\"C:/Users/<USER>/Desktop/crmeb/view/merPC/mer/node_modules/@babel/runtime/helpers/toConsumableArray.js\"));\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n// import leaveuKeyTerms from '@/config/leaveuKeyTerms.js';\nvar _default2 = exports.default = {\n  name: 'ProductSpecs',\n  props: {\n    formValidate: {\n      type: Object,\n      default: function _default() {\n        return {\n          spec_type: 0\n        };\n      }\n    },\n    ManyAttrValue: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    changeAttrValue: {\n      type: String,\n      default: function _default() {\n        return \"\";\n      }\n    },\n    attrValue: {\n      type: Object,\n      default: function _default() {}\n    },\n    formThead: {\n      type: Object,\n      default: function _default() {}\n    },\n    oneFormBatch: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    OneattrValue: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    formDynamic: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    },\n    product_id: {\n      type: String,\n      default: \"\"\n    },\n    attrs: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    cdkeyLibraryList: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    selectedLibrary: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    }\n  },\n  data: function data() {\n    return {\n      ruleList: [],\n      showAdvancedPackages: false,\n      packageConfig: {\n        basic: {\n          enabled: true,\n          name: '基础包',\n          description: '',\n          deliveryTime: '7',\n          revisions: '1',\n          price: 0\n        },\n        standard: {\n          enabled: false,\n          name: '标准包',\n          description: '',\n          deliveryTime: '5',\n          revisions: '3',\n          price: 0\n        },\n        premium: {\n          enabled: false,\n          name: '高级包',\n          description: '',\n          deliveryTime: '3',\n          revisions: 'unlimited',\n          price: 0\n        }\n      },\n      extraServices: {\n        fastDelivery: {\n          enabled: false,\n          deliveryTime: '1',\n          price: 0\n        },\n        extraRevisions: {\n          enabled: false,\n          revisions: '2',\n          price: 0\n        }\n      }\n    };\n  },\n  watch: {\n    'formValidate.spec_type': {\n      handler: function handler(newVal) {\n        if (newVal === 2) {\n          // 当切换到服务包模式时，自动生成规格数据\n          this.generateServicePackageSpecs();\n        }\n      },\n      immediate: true\n    },\n    packageConfig: {\n      handler: function handler(newVal) {\n        if (this.formValidate.spec_type === 2) {\n          this.generateServicePackageSpecs();\n        }\n        // 保存到父组件的缓存中\n        this.$emit('updatePackageConfig', newVal);\n      },\n      deep: true\n    },\n    extraServices: {\n      handler: function handler(newVal) {\n        if (this.formValidate.spec_type === 2) {\n          this.generateServicePackageSpecs();\n        }\n        // 保存到父组件的缓存中\n        this.$emit('updateExtraServices', newVal);\n      },\n      deep: true\n    },\n    // 监听从父组件传入的缓存数据\n    attrs: {\n      handler: function handler(newVal) {\n        if (this.formValidate.spec_type === 2 && newVal && newVal.length > 0) {\n          this.loadFromCache();\n        }\n      },\n      immediate: true\n    }\n  },\n  mounted: function mounted() {\n    this.productGetRule();\n  },\n  methods: {\n    // 获取商品属性模板\n    productGetRule: function productGetRule() {\n      // 这里应该调用API获取规格模板列表\n      // templateLsitApi().then(res => {\n      //   this.ruleList = res.data;\n      // });\n      this.ruleList = [];\n    },\n    // 包配置切换\n    onPackageToggle: function onPackageToggle(packageType) {\n      if (packageType === 'basic' && !this.packageConfig.basic.enabled) {\n        this.showAdvancedPackages = false;\n        this.packageConfig.standard.enabled = false;\n        this.packageConfig.premium.enabled = false;\n      }\n    },\n    // 创建高级包\n    createAdvancedPackages: function createAdvancedPackages() {\n      this.showAdvancedPackages = true;\n      this.packageConfig.standard.enabled = true;\n      this.packageConfig.premium.enabled = true;\n    },\n    // 生成服务包规格数据\n    generateServicePackageSpecs: function generateServicePackageSpecs() {\n      if (this.formValidate.spec_type !== 2) return;\n      var attrs = [];\n\n      // 生成Package规格\n      var packages = [];\n      if (this.packageConfig.basic.enabled) packages.push('Basic');\n      if (this.packageConfig.standard.enabled) packages.push('Standard');\n      if (this.packageConfig.premium.enabled) packages.push('Premium');\n      if (packages.length > 0) {\n        attrs.push({\n          value: 'Package',\n          detail: packages.map(function (pkg) {\n            return {\n              value: pkg,\n              pic: ''\n            };\n          }),\n          add_pic: 0\n        });\n      }\n\n      // 生成快速交付规格\n      if (this.extraServices.fastDelivery.enabled) {\n        attrs.push({\n          value: 'Fast Delivery',\n          detail: [{\n            value: 'No',\n            pic: ''\n          }, {\n            value: 'Yes',\n            pic: ''\n          }],\n          add_pic: 0\n        });\n      }\n\n      // 生成额外修改规格\n      if (this.extraServices.extraRevisions.enabled) {\n        attrs.push({\n          value: 'Extra Revisions',\n          detail: [{\n            value: 'No',\n            pic: ''\n          }, {\n            value: 'Yes',\n            pic: ''\n          }],\n          add_pic: 0\n        });\n      }\n\n      // 触发父组件更新规格数据\n      this.$emit('setAttrs', attrs);\n    },\n    // 生成规格组合\n    generateAttrCombinations: function generateAttrCombinations(attrs) {\n      var _this = this;\n      if (attrs.length === 0) return [];\n      var combinations = [];\n      var _generateCombination = function generateCombination(index, current) {\n        if (index === attrs.length) {\n          combinations.push((0, _toConsumableArray2.default)(current));\n          return;\n        }\n        var _iterator = (0, _createForOfIteratorHelper2.default)(attrs[index].detail),\n          _step;\n        try {\n          for (_iterator.s(); !(_step = _iterator.n()).done;) {\n            var detail = _step.value;\n            current.push(detail.value);\n            _generateCombination(index + 1, current);\n            current.pop();\n          }\n        } catch (err) {\n          _iterator.e(err);\n        } finally {\n          _iterator.f();\n        }\n      };\n      _generateCombination(0, []);\n\n      // 为每个组合生成价格和库存信息，符合现有系统的数据格式\n      var attrValues = combinations.map(function (combination, index) {\n        var price = _this.calculateCombinationPrice(combination);\n        var row = {\n          attr_arr: combination,\n          detail: {},\n          cdkey: {},\n          title: \"\",\n          key: \"\",\n          price: price,\n          image: \"\",\n          ot_price: price * 1.2,\n          // 划线价为售价的120%\n          cost: price * 0.7,\n          // 成本价为售价的70%\n          stock: 999,\n          is_show: 1,\n          is_default_select: index === 0 ? 1 : 0,\n          // 第一个组合默认选中\n          unique: \"\",\n          weight: 0,\n          volume: 0,\n          extension_one: 0,\n          extension_two: 0,\n          svip_price: price * 0.9,\n          // 会员价为售价的90%\n          bar_code: '',\n          bar_code_number: ''\n        };\n\n        // 构建detail对象\n        for (var i = 0; i < combination.length; i++) {\n          var value = combination[i];\n          row.detail[attrs[i].value] = value;\n          row.title = attrs[i].value;\n          row.key = attrs[i].value;\n        }\n        return row;\n      });\n\n      // 不直接emit，而是让父组件的generateAttr方法处理\n      return attrValues;\n    },\n    // 计算组合价格\n    calculateCombinationPrice: function calculateCombinationPrice(combination) {\n      var basePrice = 0;\n\n      // 根据包类型确定基础价格\n      if (combination.includes('Basic')) {\n        basePrice = this.packageConfig.basic.price;\n      } else if (combination.includes('Standard')) {\n        basePrice = this.packageConfig.standard.price;\n      } else if (combination.includes('Premium')) {\n        basePrice = this.packageConfig.premium.price;\n      }\n\n      // 添加额外服务费用\n      if (combination.includes('Yes')) {\n        if (combination.some(function (item) {\n          return item === 'Yes';\n        }) && this.extraServices.fastDelivery.enabled) {\n          basePrice += this.extraServices.fastDelivery.price;\n        }\n        if (combination.some(function (item) {\n          return item === 'Yes';\n        }) && this.extraServices.extraRevisions.enabled) {\n          basePrice += this.extraServices.extraRevisions.price;\n        }\n      }\n      return basePrice;\n    },\n    // 选择规格模板\n    confirm: function confirm(templateId) {\n      // 这里应该调用API获取模板详情并应用\n      console.log('Selected template:', templateId);\n    },\n    // 从缓存加载数据\n    loadFromCache: function loadFromCache() {\n      if (!this.attrs || this.attrs.length === 0) return;\n\n      // 检查是否有Package规格，如果有则说明是服务包模式\n      var packageAttr = this.attrs.find(function (attr) {\n        return attr.value === 'Package';\n      });\n      if (!packageAttr) return;\n\n      // 恢复套餐配置\n      var packages = packageAttr.detail.map(function (d) {\n        return d.value;\n      });\n      if (packages.includes('Basic')) {\n        this.packageConfig.basic.enabled = true;\n      }\n      if (packages.includes('Standard')) {\n        this.packageConfig.standard.enabled = true;\n        this.showAdvancedPackages = true;\n      }\n      if (packages.includes('Premium')) {\n        this.packageConfig.premium.enabled = true;\n        this.showAdvancedPackages = true;\n      }\n\n      // 恢复额外服务配置\n      var fastDeliveryAttr = this.attrs.find(function (attr) {\n        return attr.value === 'Fast Delivery';\n      });\n      if (fastDeliveryAttr) {\n        this.extraServices.fastDelivery.enabled = true;\n      }\n      var extraRevisionsAttr = this.attrs.find(function (attr) {\n        return attr.value === 'Extra Revisions';\n      });\n      if (extraRevisionsAttr) {\n        this.extraServices.extraRevisions.enabled = true;\n      }\n\n      // 从ManyAttrValue中恢复价格信息\n      if (this.ManyAttrValue && this.ManyAttrValue.length > 0) {\n        this.restorePricesFromAttrValue();\n      }\n    },\n    // 从规格值中恢复价格信息\n    restorePricesFromAttrValue: function restorePricesFromAttrValue() {\n      var _this2 = this;\n      this.ManyAttrValue.forEach(function (item) {\n        var detail = item.detail || {};\n\n        // 恢复套餐价格\n        if (detail.Package) {\n          var packageType = detail.Package.toLowerCase();\n          if (_this2.packageConfig[packageType]) {\n            _this2.packageConfig[packageType].price = item.price || 0;\n          }\n        }\n      });\n    },\n    // 验证服务包数据\n    validateServicePackage: function validateServicePackage() {\n      if (this.formValidate.spec_type !== 2) return true;\n\n      // 检查是否至少启用了基础包\n      if (!this.packageConfig.basic.enabled) {\n        this.$message.error('请至少启用基础包');\n        return false;\n      }\n\n      // 检查基础包是否填写完整\n      var basic = this.packageConfig.basic;\n      if (!basic.name || !basic.description || basic.price <= 0) {\n        this.$message.error('请完整填写基础包信息');\n        return false;\n      }\n\n      // 检查启用的高级包是否填写完整\n      if (this.packageConfig.standard.enabled) {\n        var standard = this.packageConfig.standard;\n        if (!standard.name || !standard.description || standard.price <= 0) {\n          this.$message.error('请完整填写标准包信息');\n          return false;\n        }\n      }\n      if (this.packageConfig.premium.enabled) {\n        var premium = this.packageConfig.premium;\n        if (!premium.name || !premium.description || premium.price <= 0) {\n          this.$message.error('请完整填写高级包信息');\n          return false;\n        }\n      }\n      return true;\n    }\n  }\n};", null]}