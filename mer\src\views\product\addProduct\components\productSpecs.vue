<!-- 商品规格 -->
<template>
  <div>
    <el-row>
      <el-col :span="24">
        <el-form-item :label="$t('规格类型：')" props="spec_type">
          <el-radio-group v-model="formValidate.spec_type">
            <el-radio :label="0" class="radio">{{ $t('单规格') }}</el-radio>
            <el-radio :label="1">{{ $t('多规格') }}</el-radio>
            <el-radio :label="2">{{ $t('服务包模式') }}</el-radio>
          </el-radio-group>
          <el-dropdown
            v-if="formValidate.spec_type == 1 && ruleList.length > 0"
            class="ml20"
            @command="confirm"
            trigger="hover"
          >
            <span class="el-dropdown-link">{{ $t('选择规格模板') }}<i class="el-icon-arrow-down el-icon--right"></i
            ></span>
            <el-dropdown-menu slot="dropdown">
              <el-scrollbar style="max-height: 300px;overflow-y:scroll;">
                <el-dropdown-item
                  v-for="(item, index) in ruleList"
                  :key="index"
                  :command="item.attr_template_id"
                >
                  {{ item.template_name }}
                </el-dropdown-item>
              </el-scrollbar>
            </el-dropdown-menu>
          </el-dropdown>
        </el-form-item>
      </el-col>

      <!-- 服务包模式设置 -->
      <el-col :span="24" v-if="formValidate.spec_type === 2" class="noForm">
        <el-form-item :label="$t('服务包配置：')" required>
          <div class="service-packages">
            <!-- 基础包配置 -->
            <div class="package-card basic-package">
              <div class="package-header">
                <h3>{{ $t('基础包 (Basic)') }}</h3>
                <el-switch
                  v-model="packageConfig.basic.enabled"
                  :active-text="$t('启用')"
                  :inactive-text="$t('禁用')"
                  @change="onPackageToggle('basic')"
                />
              </div>
              <div v-if="packageConfig.basic.enabled" class="package-content">
                <el-form :model="packageConfig.basic" label-width="100px" size="small">
                  <el-form-item :label="$t('包名称')">
                    <el-input v-model="packageConfig.basic.name" :placeholder="$t('请输入包名称')" />
                  </el-form-item>
                  <el-form-item :label="$t('包描述')">
                    <el-input
                      type="textarea"
                      v-model="packageConfig.basic.description"
                      :placeholder="$t('请输入包描述')"
                      :rows="3"
                    />
                  </el-form-item>
                  <el-form-item :label="$t('交付时间')">
                    <el-select v-model="packageConfig.basic.deliveryTime" :placeholder="$t('请选择交付时间')">
                      <el-option label="1天" value="1"></el-option>
                      <el-option label="2天" value="2"></el-option>
                      <el-option label="3天" value="3"></el-option>
                      <el-option label="5天" value="5"></el-option>
                      <el-option label="7天" value="7"></el-option>
                      <el-option label="10天" value="10"></el-option>
                      <el-option label="15天" value="15"></el-option>
                      <el-option label="30天" value="30"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item :label="$t('修改次数')">
                    <el-select v-model="packageConfig.basic.revisions" :placeholder="$t('请选择修改次数')">
                      <el-option label="不包含修改" value="0"></el-option>
                      <el-option label="1次修改" value="1"></el-option>
                      <el-option label="2次修改" value="2"></el-option>
                      <el-option label="3次修改" value="3"></el-option>
                      <el-option label="5次修改" value="5"></el-option>
                      <el-option label="无限修改" value="unlimited"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item :label="$t('价格')">
                    <el-input-number
                      v-model="packageConfig.basic.price"
                      :min="0"
                      :precision="2"
                      controls-position="right"
                    />
                  </el-form-item>
                </el-form>
              </div>
            </div>

            <!-- 标准包和高级包配置按钮 -->
            <div class="package-actions" v-if="packageConfig.basic.enabled">
              <el-button
                v-if="!showAdvancedPackages"
                type="primary"
                @click="createAdvancedPackages"
                class="create-packages-btn"
              >
                {{ $t('Create Packages') }}
              </el-button>
              <div v-if="showAdvancedPackages" class="advanced-packages">
                <!-- 标准包配置 -->
                <div class="package-card standard-package">
                  <div class="package-header">
                    <h3>{{ $t('标准包 (Standard)') }}</h3>
                    <el-switch
                      v-model="packageConfig.standard.enabled"
                      :active-text="$t('启用')"
                      :inactive-text="$t('禁用')"
                    />
                  </div>
                  <div v-if="packageConfig.standard.enabled" class="package-content">
                    <el-form :model="packageConfig.standard" label-width="100px" size="small">
                      <el-form-item :label="$t('包名称')">
                        <el-input v-model="packageConfig.standard.name" :placeholder="$t('请输入包名称')" />
                      </el-form-item>
                      <el-form-item :label="$t('包描述')">
                        <el-input
                          type="textarea"
                          v-model="packageConfig.standard.description"
                          :placeholder="$t('请输入包描述')"
                          :rows="3"
                        />
                      </el-form-item>
                      <el-form-item :label="$t('交付时间')">
                        <el-select v-model="packageConfig.standard.deliveryTime" :placeholder="$t('请选择交付时间')">
                          <el-option label="1天" value="1"></el-option>
                          <el-option label="2天" value="2"></el-option>
                          <el-option label="3天" value="3"></el-option>
                          <el-option label="5天" value="5"></el-option>
                          <el-option label="7天" value="7"></el-option>
                          <el-option label="10天" value="10"></el-option>
                          <el-option label="15天" value="15"></el-option>
                          <el-option label="30天" value="30"></el-option>
                        </el-select>
                      </el-form-item>
                      <el-form-item :label="$t('修改次数')">
                        <el-select v-model="packageConfig.standard.revisions" :placeholder="$t('请选择修改次数')">
                          <el-option label="不包含修改" value="0"></el-option>
                          <el-option label="1次修改" value="1"></el-option>
                          <el-option label="2次修改" value="2"></el-option>
                          <el-option label="3次修改" value="3"></el-option>
                          <el-option label="5次修改" value="5"></el-option>
                          <el-option label="无限修改" value="unlimited"></el-option>
                        </el-select>
                      </el-form-item>
                      <el-form-item :label="$t('价格')">
                        <el-input-number
                          v-model="packageConfig.standard.price"
                          :min="0"
                          :precision="2"
                          controls-position="right"
                        />
                      </el-form-item>
                    </el-form>
                  </div>
                </div>

                <!-- 高级包配置 -->
                <div class="package-card premium-package">
                  <div class="package-header">
                    <h3>{{ $t('高级包 (Premium)') }}</h3>
                    <el-switch
                      v-model="packageConfig.premium.enabled"
                      :active-text="$t('启用')"
                      :inactive-text="$t('禁用')"
                    />
                  </div>
                  <div v-if="packageConfig.premium.enabled" class="package-content">
                    <el-form :model="packageConfig.premium" label-width="100px" size="small">
                      <el-form-item :label="$t('包名称')">
                        <el-input v-model="packageConfig.premium.name" :placeholder="$t('请输入包名称')" />
                      </el-form-item>
                      <el-form-item :label="$t('包描述')">
                        <el-input
                          type="textarea"
                          v-model="packageConfig.premium.description"
                          :placeholder="$t('请输入包描述')"
                          :rows="3"
                        />
                      </el-form-item>
                      <el-form-item :label="$t('交付时间')">
                        <el-select v-model="packageConfig.premium.deliveryTime" :placeholder="$t('请选择交付时间')">
                          <el-option label="1天" value="1"></el-option>
                          <el-option label="2天" value="2"></el-option>
                          <el-option label="3天" value="3"></el-option>
                          <el-option label="5天" value="5"></el-option>
                          <el-option label="7天" value="7"></el-option>
                          <el-option label="10天" value="10"></el-option>
                          <el-option label="15天" value="15"></el-option>
                          <el-option label="30天" value="30"></el-option>
                        </el-select>
                      </el-form-item>
                      <el-form-item :label="$t('修改次数')">
                        <el-select v-model="packageConfig.premium.revisions" :placeholder="$t('请选择修改次数')">
                          <el-option label="不包含修改" value="0"></el-option>
                          <el-option label="1次修改" value="1"></el-option>
                          <el-option label="2次修改" value="2"></el-option>
                          <el-option label="3次修改" value="3"></el-option>
                          <el-option label="5次修改" value="5"></el-option>
                          <el-option label="无限修改" value="unlimited"></el-option>
                        </el-select>
                      </el-form-item>
                      <el-form-item :label="$t('价格')">
                        <el-input-number
                          v-model="packageConfig.premium.price"
                          :min="0"
                          :precision="2"
                          controls-position="right"
                        />
                      </el-form-item>
                    </el-form>
                  </div>
                </div>
              </div>
            </div>

            <!-- 额外服务配置 -->
            <div class="extra-services" v-if="showAdvancedPackages">
              <h3>{{ $t('额外服务 (Extra Services)') }}</h3>
              <div class="extra-service-item">
                <el-checkbox v-model="extraServices.fastDelivery.enabled">
                  {{ $t('快速交付') }}
                </el-checkbox>
                <div v-if="extraServices.fastDelivery.enabled" class="extra-service-config">
                  <el-form :model="extraServices.fastDelivery" label-width="100px" size="small" inline>
                    <el-form-item :label="$t('交付时间')">
                      <el-select v-model="extraServices.fastDelivery.deliveryTime">
                        <el-option label="24小时" value="1"></el-option>
                        <el-option label="2天" value="2"></el-option>
                        <el-option label="3天" value="3"></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item :label="$t('额外费用')">
                      <el-input-number
                        v-model="extraServices.fastDelivery.price"
                        :min="0"
                        :precision="2"
                        controls-position="right"
                      />
                    </el-form-item>
                  </el-form>
                </div>
              </div>

              <div class="extra-service-item">
                <el-checkbox v-model="extraServices.extraRevisions.enabled">
                  {{ $t('额外修改次数') }}
                </el-checkbox>
                <div v-if="extraServices.extraRevisions.enabled" class="extra-service-config">
                  <el-form :model="extraServices.extraRevisions" label-width="100px" size="small" inline>
                    <el-form-item :label="$t('修改次数')">
                      <el-select v-model="extraServices.extraRevisions.revisions">
                        <el-option label="1次修改" value="1"></el-option>
                        <el-option label="2次修改" value="2"></el-option>
                        <el-option label="3次修改" value="3"></el-option>
                        <el-option label="5次修改" value="5"></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item :label="$t('额外费用')">
                      <el-input-number
                        v-model="extraServices.extraRevisions.price"
                        :min="0"
                        :precision="2"
                        controls-position="right"
                      />
                    </el-form-item>
                  </el-form>
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-col>

      <!-- 原有的多规格设置 -->
      <el-col :span="24" v-if="formValidate.spec_type === 1" class="noForm">
        <el-form-item :label="$t('商品规格：')" required>
          <div class="specifications">
            <div class="spec-placeholder">
              <p>{{ $t('多规格模式暂未在此组件中实现，请使用原有的规格设置组件') }}</p>
            </div>
          </div>
        </el-form-item>
      </el-col>

      <!-- 单规格表格 -->
      <el-col :xl="24" :lg="24" :md="24" :sm="24" :xs="24" v-if="formValidate.spec_type === 0">
        <el-form-item>
          <div class="single-spec-placeholder">
            <p>{{ $t('单规格模式暂未在此组件中实现，请使用原有的规格设置组件') }}</p>
          </div>
        </el-form-item>
      </el-col>
    </el-row>
  </div>
</template>

<script>
// import leaveuKeyTerms from '@/config/leaveuKeyTerms.js';

export default {
  name: 'ProductSpecs',
  props: {
    formValidate: {
      type: Object,
      default: () => ({
        spec_type: 0
      })
    },
    ManyAttrValue: {
      type: Array,
      default: () => []
    },
    changeAttrValue: {
      type: String,
      default: () => ""
    },
    attrValue: {
      type: Object,
      default: () => {}
    },
    formThead: {
      type: Object,
      default: () => {}
    },
    oneFormBatch: {
      type: Array,
      default: () => []
    },
    OneattrValue: {
      type: Array,
      default: () => []
    },
    formDynamic: {
      type: Object,
      default: () => ({})
    },
    product_id: {
      type: String,
      default: ""
    },
    attrs: {
      type: Array,
      default: () => []
    },
    cdkeyLibraryList: {
      type: Array,
      default: () => []
    },
    selectedLibrary: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      ruleList: [],
      showAdvancedPackages: false,
      packageConfig: {
        basic: {
          enabled: true,
          name: '基础包',
          description: '',
          deliveryTime: '7',
          revisions: '1',
          price: 0
        },
        standard: {
          enabled: false,
          name: '标准包',
          description: '',
          deliveryTime: '5',
          revisions: '3',
          price: 0
        },
        premium: {
          enabled: false,
          name: '高级包',
          description: '',
          deliveryTime: '3',
          revisions: 'unlimited',
          price: 0
        }
      },
      extraServices: {
        fastDelivery: {
          enabled: false,
          deliveryTime: '1',
          price: 0
        },
        extraRevisions: {
          enabled: false,
          revisions: '2',
          price: 0
        }
      }
    };
  },
  watch: {
    'formValidate.spec_type': {
      handler(newVal) {
        if (newVal === 2) {
          // 当切换到服务包模式时，自动生成规格数据
          this.generateServicePackageSpecs();
        }
      },
      immediate: true
    },
    packageConfig: {
      handler() {
        if (this.formValidate.spec_type === 2) {
          this.generateServicePackageSpecs();
        }
      },
      deep: true
    },
    extraServices: {
      handler() {
        if (this.formValidate.spec_type === 2) {
          this.generateServicePackageSpecs();
        }
      },
      deep: true
    }
  },
  mounted() {
    this.productGetRule();
  },
  methods: {
    // 获取商品属性模板
    productGetRule() {
      // 这里应该调用API获取规格模板列表
      // templateLsitApi().then(res => {
      //   this.ruleList = res.data;
      // });
      this.ruleList = [];
    },

    // 包配置切换
    onPackageToggle(packageType) {
      if (packageType === 'basic' && !this.packageConfig.basic.enabled) {
        this.showAdvancedPackages = false;
        this.packageConfig.standard.enabled = false;
        this.packageConfig.premium.enabled = false;
      }
    },

    // 创建高级包
    createAdvancedPackages() {
      this.showAdvancedPackages = true;
      this.packageConfig.standard.enabled = true;
      this.packageConfig.premium.enabled = true;
    },

    // 生成服务包规格数据
    generateServicePackageSpecs() {
      if (this.formValidate.spec_type !== 2) return;

      const attrs = [];

      // 生成Package规格
      const packages = [];
      if (this.packageConfig.basic.enabled) packages.push('Basic');
      if (this.packageConfig.standard.enabled) packages.push('Standard');
      if (this.packageConfig.premium.enabled) packages.push('Premium');

      if (packages.length > 0) {
        attrs.push({
          value: 'Package',
          detail: packages.map(pkg => ({ value: pkg, pic: '' })),
          add_pic: 0
        });
      }

      // 生成快速交付规格
      if (this.extraServices.fastDelivery.enabled) {
        attrs.push({
          value: 'Fast Delivery',
          detail: [
            { value: 'No', pic: '' },
            { value: 'Yes', pic: '' }
          ],
          add_pic: 0
        });
      }

      // 生成额外修改规格
      if (this.extraServices.extraRevisions.enabled) {
        attrs.push({
          value: 'Extra Revisions',
          detail: [
            { value: 'No', pic: '' },
            { value: 'Yes', pic: '' }
          ],
          add_pic: 0
        });
      }

      // 触发父组件更新规格数据
      this.$emit('setAttrs', attrs);
    },

    // 生成规格组合
    generateAttrCombinations(attrs) {
      if (attrs.length === 0) return [];

      const combinations = [];
      const generateCombination = (index, current) => {
        if (index === attrs.length) {
          combinations.push([...current]);
          return;
        }

        for (const detail of attrs[index].detail) {
          current.push(detail.value);
          generateCombination(index + 1, current);
          current.pop();
        }
      };

      generateCombination(0, []);

      // 为每个组合生成价格和库存信息，符合现有系统的数据格式
      const attrValues = combinations.map((combination, index) => {
        const price = this.calculateCombinationPrice(combination);
        const row = {
          attr_arr: combination,
          detail: {},
          cdkey: {},
          title: "",
          key: "",
          price: price,
          image: "",
          ot_price: price * 1.2, // 划线价为售价的120%
          cost: price * 0.7, // 成本价为售价的70%
          stock: 999,
          is_show: 1,
          is_default_select: index === 0 ? 1 : 0, // 第一个组合默认选中
          unique: "",
          weight: 0,
          volume: 0,
          extension_one: 0,
          extension_two: 0,
          svip_price: price * 0.9, // 会员价为售价的90%
          bar_code: '',
          bar_code_number: ''
        };

        // 构建detail对象
        for (let i = 0; i < combination.length; i++) {
          const value = combination[i];
          row.detail[attrs[i].value] = value;
          row.title = attrs[i].value;
          row.key = attrs[i].value;
        }

        return row;
      });

      // 不直接emit，而是让父组件的generateAttr方法处理
      return attrValues;
    },

    // 计算组合价格
    calculateCombinationPrice(combination) {
      let basePrice = 0;

      // 根据包类型确定基础价格
      if (combination.includes('Basic')) {
        basePrice = this.packageConfig.basic.price;
      } else if (combination.includes('Standard')) {
        basePrice = this.packageConfig.standard.price;
      } else if (combination.includes('Premium')) {
        basePrice = this.packageConfig.premium.price;
      }

      // 添加额外服务费用
      if (combination.includes('Yes')) {
        if (combination.some(item => item === 'Yes') && this.extraServices.fastDelivery.enabled) {
          basePrice += this.extraServices.fastDelivery.price;
        }
        if (combination.some(item => item === 'Yes') && this.extraServices.extraRevisions.enabled) {
          basePrice += this.extraServices.extraRevisions.price;
        }
      }

      return basePrice;
    },

    // 选择规格模板
    confirm(templateId) {
      // 这里应该调用API获取模板详情并应用
      console.log('Selected template:', templateId);
    }
  }
};
</script>

<style lang="scss" scoped>
.service-packages {
  .package-card {
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    margin-bottom: 20px;
    background: #fff;

    .package-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 20px;
      border-bottom: 1px solid #e4e7ed;
      background: #f8f9fa;
      border-radius: 8px 8px 0 0;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
      }
    }

    .package-content {
      padding: 20px;
    }

    &.basic-package .package-header {
      background: #e8f5e8;
      border-color: #c3e6c3;

      h3 {
        color: #52c41a;
      }
    }

    &.standard-package .package-header {
      background: #e6f7ff;
      border-color: #91d5ff;

      h3 {
        color: #1890ff;
      }
    }

    &.premium-package .package-header {
      background: #fff2e8;
      border-color: #ffd591;

      h3 {
        color: #fa8c16;
      }
    }
  }

  .package-actions {
    margin: 20px 0;
    text-align: center;

    .create-packages-btn {
      padding: 12px 30px;
      font-size: 16px;
      font-weight: 600;
    }
  }

  .advanced-packages {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 20px;
  }

  .extra-services {
    margin-top: 30px;
    padding: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    background: #fafafa;

    h3 {
      margin: 0 0 20px 0;
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }

    .extra-service-item {
      margin-bottom: 20px;

      .extra-service-config {
        margin-top: 10px;
        padding: 15px;
        background: #fff;
        border: 1px solid #e4e7ed;
        border-radius: 6px;
      }
    }
  }
}

@media (max-width: 768px) {
  .service-packages {
    .advanced-packages {
      grid-template-columns: 1fr;
    }
  }
}

.spec-placeholder,
.single-spec-placeholder {
  padding: 40px;
  text-align: center;
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  color: #6c757d;

  p {
    margin: 0;
    font-size: 14px;
  }
}
</style>