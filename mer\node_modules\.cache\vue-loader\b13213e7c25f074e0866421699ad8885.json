{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\components\\productSpecs.vue?vue&type=template&id=f7b5f20a&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\components\\productSpecs.vue", "mtime": 1750417751492}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div>\n  <el-row>\n    <el-col :span=\"24\">\n      <el-form-item :label=\"$t('规格类型：')\" props=\"spec_type\">\n        <el-radio-group v-model=\"formValidate.spec_type\">\n          <el-radio :label=\"0\" class=\"radio\">{{ $t('单规格') }}</el-radio>\n          <el-radio :label=\"1\">{{ $t('多规格') }}</el-radio>\n          <el-radio :label=\"2\">{{ $t('服务包模式') }}</el-radio>\n        </el-radio-group>\n        <el-dropdown\n          v-if=\"formValidate.spec_type == 1 && ruleList.length > 0\"\n          class=\"ml20\"\n          @command=\"confirm\"\n          trigger=\"hover\"\n        >\n          <span class=\"el-dropdown-link\">{{ $t('选择规格模板') }}<i class=\"el-icon-arrow-down el-icon--right\"></i\n          ></span>\n          <el-dropdown-menu slot=\"dropdown\">\n            <el-scrollbar style=\"max-height: 300px;overflow-y:scroll;\">\n              <el-dropdown-item\n                v-for=\"(item, index) in ruleList\"\n                :key=\"index\"\n                :command=\"item.attr_template_id\"\n              >\n                {{ item.template_name }}\n              </el-dropdown-item>\n            </el-scrollbar>\n          </el-dropdown-menu>\n        </el-dropdown>\n      </el-form-item>\n    </el-col>\n\n    <!-- 服务包模式设置 -->\n    <el-col :span=\"24\" v-if=\"formValidate.spec_type === 2\" class=\"noForm\">\n      <el-form-item :label=\"$t('服务包配置：')\" required>\n        <div class=\"service-packages\">\n          <!-- 基础包配置 -->\n          <div class=\"package-card basic-package\">\n            <div class=\"package-header\">\n              <h3>{{ $t('基础包 (Basic)') }}</h3>\n              <el-switch\n                v-model=\"packageConfig.basic.enabled\"\n                :active-text=\"$t('启用')\"\n                :inactive-text=\"$t('禁用')\"\n                @change=\"onPackageToggle('basic')\"\n              />\n            </div>\n            <div v-if=\"packageConfig.basic.enabled\" class=\"package-content\">\n              <el-form :model=\"packageConfig.basic\" label-width=\"100px\" size=\"small\">\n                <el-form-item :label=\"$t('包名称')\">\n                  <el-input v-model=\"packageConfig.basic.name\" :placeholder=\"$t('请输入包名称')\" />\n                </el-form-item>\n                <el-form-item :label=\"$t('包描述')\">\n                  <el-input\n                    type=\"textarea\"\n                    v-model=\"packageConfig.basic.description\"\n                    :placeholder=\"$t('请输入包描述')\"\n                    :rows=\"3\"\n                  />\n                </el-form-item>\n                <el-form-item :label=\"$t('交付时间')\">\n                  <el-select v-model=\"packageConfig.basic.deliveryTime\" :placeholder=\"$t('请选择交付时间')\">\n                    <el-option label=\"1天\" value=\"1\"></el-option>\n                    <el-option label=\"2天\" value=\"2\"></el-option>\n                    <el-option label=\"3天\" value=\"3\"></el-option>\n                    <el-option label=\"5天\" value=\"5\"></el-option>\n                    <el-option label=\"7天\" value=\"7\"></el-option>\n                    <el-option label=\"10天\" value=\"10\"></el-option>\n                    <el-option label=\"15天\" value=\"15\"></el-option>\n                    <el-option label=\"30天\" value=\"30\"></el-option>\n                  </el-select>\n                </el-form-item>\n                <el-form-item :label=\"$t('修改次数')\">\n                  <el-select v-model=\"packageConfig.basic.revisions\" :placeholder=\"$t('请选择修改次数')\">\n                    <el-option label=\"不包含修改\" value=\"0\"></el-option>\n                    <el-option label=\"1次修改\" value=\"1\"></el-option>\n                    <el-option label=\"2次修改\" value=\"2\"></el-option>\n                    <el-option label=\"3次修改\" value=\"3\"></el-option>\n                    <el-option label=\"5次修改\" value=\"5\"></el-option>\n                    <el-option label=\"无限修改\" value=\"unlimited\"></el-option>\n                  </el-select>\n                </el-form-item>\n                <el-form-item :label=\"$t('价格')\">\n                  <el-input-number\n                    v-model=\"packageConfig.basic.price\"\n                    :min=\"0\"\n                    :precision=\"2\"\n                    controls-position=\"right\"\n                  />\n                </el-form-item>\n              </el-form>\n            </div>\n          </div>\n\n          <!-- 标准包和高级包配置按钮 -->\n          <div class=\"package-actions\" v-if=\"packageConfig.basic.enabled\">\n            <el-button\n              v-if=\"!showAdvancedPackages\"\n              type=\"primary\"\n              @click=\"createAdvancedPackages\"\n              class=\"create-packages-btn\"\n            >\n              {{ $t('Create Packages') }}\n            </el-button>\n            <div v-if=\"showAdvancedPackages\" class=\"advanced-packages\">\n              <!-- 标准包配置 -->\n              <div class=\"package-card standard-package\">\n                <div class=\"package-header\">\n                  <h3>{{ $t('标准包 (Standard)') }}</h3>\n                  <el-switch\n                    v-model=\"packageConfig.standard.enabled\"\n                    :active-text=\"$t('启用')\"\n                    :inactive-text=\"$t('禁用')\"\n                  />\n                </div>\n                <div v-if=\"packageConfig.standard.enabled\" class=\"package-content\">\n                  <el-form :model=\"packageConfig.standard\" label-width=\"100px\" size=\"small\">\n                    <el-form-item :label=\"$t('包名称')\">\n                      <el-input v-model=\"packageConfig.standard.name\" :placeholder=\"$t('请输入包名称')\" />\n                    </el-form-item>\n                    <el-form-item :label=\"$t('包描述')\">\n                      <el-input\n                        type=\"textarea\"\n                        v-model=\"packageConfig.standard.description\"\n                        :placeholder=\"$t('请输入包描述')\"\n                        :rows=\"3\"\n                      />\n                    </el-form-item>\n                    <el-form-item :label=\"$t('交付时间')\">\n                      <el-select v-model=\"packageConfig.standard.deliveryTime\" :placeholder=\"$t('请选择交付时间')\">\n                        <el-option label=\"1天\" value=\"1\"></el-option>\n                        <el-option label=\"2天\" value=\"2\"></el-option>\n                        <el-option label=\"3天\" value=\"3\"></el-option>\n                        <el-option label=\"5天\" value=\"5\"></el-option>\n                        <el-option label=\"7天\" value=\"7\"></el-option>\n                        <el-option label=\"10天\" value=\"10\"></el-option>\n                        <el-option label=\"15天\" value=\"15\"></el-option>\n                        <el-option label=\"30天\" value=\"30\"></el-option>\n                      </el-select>\n                    </el-form-item>\n                    <el-form-item :label=\"$t('修改次数')\">\n                      <el-select v-model=\"packageConfig.standard.revisions\" :placeholder=\"$t('请选择修改次数')\">\n                        <el-option label=\"不包含修改\" value=\"0\"></el-option>\n                        <el-option label=\"1次修改\" value=\"1\"></el-option>\n                        <el-option label=\"2次修改\" value=\"2\"></el-option>\n                        <el-option label=\"3次修改\" value=\"3\"></el-option>\n                        <el-option label=\"5次修改\" value=\"5\"></el-option>\n                        <el-option label=\"无限修改\" value=\"unlimited\"></el-option>\n                      </el-select>\n                    </el-form-item>\n                    <el-form-item :label=\"$t('价格')\">\n                      <el-input-number\n                        v-model=\"packageConfig.standard.price\"\n                        :min=\"0\"\n                        :precision=\"2\"\n                        controls-position=\"right\"\n                      />\n                    </el-form-item>\n                  </el-form>\n                </div>\n              </div>\n\n              <!-- 高级包配置 -->\n              <div class=\"package-card premium-package\">\n                <div class=\"package-header\">\n                  <h3>{{ $t('高级包 (Premium)') }}</h3>\n                  <el-switch\n                    v-model=\"packageConfig.premium.enabled\"\n                    :active-text=\"$t('启用')\"\n                    :inactive-text=\"$t('禁用')\"\n                  />\n                </div>\n                <div v-if=\"packageConfig.premium.enabled\" class=\"package-content\">\n                  <el-form :model=\"packageConfig.premium\" label-width=\"100px\" size=\"small\">\n                    <el-form-item :label=\"$t('包名称')\">\n                      <el-input v-model=\"packageConfig.premium.name\" :placeholder=\"$t('请输入包名称')\" />\n                    </el-form-item>\n                    <el-form-item :label=\"$t('包描述')\">\n                      <el-input\n                        type=\"textarea\"\n                        v-model=\"packageConfig.premium.description\"\n                        :placeholder=\"$t('请输入包描述')\"\n                        :rows=\"3\"\n                      />\n                    </el-form-item>\n                    <el-form-item :label=\"$t('交付时间')\">\n                      <el-select v-model=\"packageConfig.premium.deliveryTime\" :placeholder=\"$t('请选择交付时间')\">\n                        <el-option label=\"1天\" value=\"1\"></el-option>\n                        <el-option label=\"2天\" value=\"2\"></el-option>\n                        <el-option label=\"3天\" value=\"3\"></el-option>\n                        <el-option label=\"5天\" value=\"5\"></el-option>\n                        <el-option label=\"7天\" value=\"7\"></el-option>\n                        <el-option label=\"10天\" value=\"10\"></el-option>\n                        <el-option label=\"15天\" value=\"15\"></el-option>\n                        <el-option label=\"30天\" value=\"30\"></el-option>\n                      </el-select>\n                    </el-form-item>\n                    <el-form-item :label=\"$t('修改次数')\">\n                      <el-select v-model=\"packageConfig.premium.revisions\" :placeholder=\"$t('请选择修改次数')\">\n                        <el-option label=\"不包含修改\" value=\"0\"></el-option>\n                        <el-option label=\"1次修改\" value=\"1\"></el-option>\n                        <el-option label=\"2次修改\" value=\"2\"></el-option>\n                        <el-option label=\"3次修改\" value=\"3\"></el-option>\n                        <el-option label=\"5次修改\" value=\"5\"></el-option>\n                        <el-option label=\"无限修改\" value=\"unlimited\"></el-option>\n                      </el-select>\n                    </el-form-item>\n                    <el-form-item :label=\"$t('价格')\">\n                      <el-input-number\n                        v-model=\"packageConfig.premium.price\"\n                        :min=\"0\"\n                        :precision=\"2\"\n                        controls-position=\"right\"\n                      />\n                    </el-form-item>\n                  </el-form>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 额外服务配置 -->\n          <div class=\"extra-services\" v-if=\"showAdvancedPackages\">\n            <h3>{{ $t('额外服务 (Extra Services)') }}</h3>\n            <div class=\"extra-service-item\">\n              <el-checkbox v-model=\"extraServices.fastDelivery.enabled\">\n                {{ $t('快速交付') }}\n              </el-checkbox>\n              <div v-if=\"extraServices.fastDelivery.enabled\" class=\"extra-service-config\">\n                <el-form :model=\"extraServices.fastDelivery\" label-width=\"100px\" size=\"small\" inline>\n                  <el-form-item :label=\"$t('交付时间')\">\n                    <el-select v-model=\"extraServices.fastDelivery.deliveryTime\">\n                      <el-option label=\"24小时\" value=\"1\"></el-option>\n                      <el-option label=\"2天\" value=\"2\"></el-option>\n                      <el-option label=\"3天\" value=\"3\"></el-option>\n                    </el-select>\n                  </el-form-item>\n                  <el-form-item :label=\"$t('额外费用')\">\n                    <el-input-number\n                      v-model=\"extraServices.fastDelivery.price\"\n                      :min=\"0\"\n                      :precision=\"2\"\n                      controls-position=\"right\"\n                    />\n                  </el-form-item>\n                </el-form>\n              </div>\n            </div>\n\n            <div class=\"extra-service-item\">\n              <el-checkbox v-model=\"extraServices.extraRevisions.enabled\">\n                {{ $t('额外修改次数') }}\n              </el-checkbox>\n              <div v-if=\"extraServices.extraRevisions.enabled\" class=\"extra-service-config\">\n                <el-form :model=\"extraServices.extraRevisions\" label-width=\"100px\" size=\"small\" inline>\n                  <el-form-item :label=\"$t('修改次数')\">\n                    <el-select v-model=\"extraServices.extraRevisions.revisions\">\n                      <el-option label=\"1次修改\" value=\"1\"></el-option>\n                      <el-option label=\"2次修改\" value=\"2\"></el-option>\n                      <el-option label=\"3次修改\" value=\"3\"></el-option>\n                      <el-option label=\"5次修改\" value=\"5\"></el-option>\n                    </el-select>\n                  </el-form-item>\n                  <el-form-item :label=\"$t('额外费用')\">\n                    <el-input-number\n                      v-model=\"extraServices.extraRevisions.price\"\n                      :min=\"0\"\n                      :precision=\"2\"\n                      controls-position=\"right\"\n                    />\n                  </el-form-item>\n                </el-form>\n              </div>\n            </div>\n          </div>\n        </div>\n      </el-form-item>\n    </el-col>\n\n    <!-- 原有的多规格设置 -->\n    <el-col :span=\"24\" v-if=\"formValidate.spec_type === 1\" class=\"noForm\">\n      <el-form-item :label=\"$t('商品规格：')\" required>\n        <div class=\"specifications\">\n          <div class=\"spec-placeholder\">\n            <p>{{ $t('多规格模式暂未在此组件中实现，请使用原有的规格设置组件') }}</p>\n          </div>\n        </div>\n      </el-form-item>\n    </el-col>\n\n    <!-- 单规格表格 -->\n    <el-col :xl=\"24\" :lg=\"24\" :md=\"24\" :sm=\"24\" :xs=\"24\" v-if=\"formValidate.spec_type === 0\">\n      <el-form-item>\n        <div class=\"single-spec-placeholder\">\n          <p>{{ $t('单规格模式暂未在此组件中实现，请使用原有的规格设置组件') }}</p>\n        </div>\n      </el-form-item>\n    </el-col>\n  </el-row>\n</div>\n", null]}