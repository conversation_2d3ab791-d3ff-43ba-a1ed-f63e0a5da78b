import Auth from './auth'

import './middleware'

// Active schemes
import scheme_3e2123be from './schemes/local.js'

export default function (ctx, inject) {
  // Options
  const options = {"resetOnError":false,"scopeKey":"scope","rewriteRedirects":true,"fullPathRedirect":false,"watchLoggedIn":true,"redirect":{"login":"/login","logout":"/","home":false,"callback":"/login"},"vuex":{"namespace":"auth"},"cookie":{"prefix":"auth.","options":{"path":"/","maxAge":604800}},"localStorage":false,"token":{"prefix":"_token."},"refresh_token":{"prefix":"_refresh_token."},"defaultStrategy":"local"}

  // Create a new Auth instance
  const $auth = new Auth(ctx, options)

  // Register strategies
  // local
  $auth.registerStrategy('local', new scheme_3e2123be($auth, {"endpoints":{"login":{"url":"/api/auth/login","method":"post","propertyName":"token"},"logout":{"url":"/api/logout","method":"post"},"user":{"url":"/api/user","method":"get","propertyName":false}},"_name":"local"}))

  // local1
  $auth.registerStrategy('local1', new scheme_3e2123be($auth, {"endpoints":{"login":{"url":"/api/auth/login","method":"post","propertyName":"token"},"logout":{"url":"/api/logout","method":"post"},"user":{"url":"/api/user","method":"get","propertyName":false}},"_name":"local1"}))

  // local2
  $auth.registerStrategy('local2', new scheme_3e2123be($auth, {"endpoints":{"login":{"url":"/api/auth/smslogin","method":"post","propertyName":"token"},"logout":{"url":"/api/logout","method":"post"},"user":{"url":"/api/user","method":"get","propertyName":false}},"_name":"local2"}))

  // Inject it to nuxt context as $auth
  inject('auth', $auth)
  ctx.$auth = $auth

  // Initialize auth
  return $auth.init().catch(error => {
    if (process.client) {
      console.error('[ERROR] [AUTH]', error)
    }
  })
}
