import Vue from 'vue'
import Router from 'vue-router'
import { interopDefault } from './utils'
import scrollBehavior from './router.scrollBehavior.js'

const _71d74d83 = () => interopDefault(import('..\\pages\\coupon_center.vue' /* webpackChunkName: "pages/coupon_center" */))
const _68834997 = () => interopDefault(import('..\\pages\\evaluation.vue' /* webpackChunkName: "pages/evaluation" */))
const _55fa121a = () => interopDefault(import('..\\pages\\goods_cate.vue' /* webpackChunkName: "pages/goods_cate" */))
const _5001c56c = () => interopDefault(import('..\\pages\\goods_coupon.vue' /* webpackChunkName: "pages/goods_coupon" */))
const _223decc2 = () => interopDefault(import('..\\pages\\goods_list.vue' /* webpackChunkName: "pages/goods_list" */))
const _769b8481 = () => interopDefault(import('..\\pages\\goods_presell.vue' /* webpackChunkName: "pages/goods_presell" */))
const _ed5fcc68 = () => interopDefault(import('..\\pages\\goods_search.vue' /* webpackChunkName: "pages/goods_search" */))
const _3995863b = () => interopDefault(import('..\\pages\\goods_seckill.vue' /* webpackChunkName: "pages/goods_seckill" */))
const _3fdf69de = () => interopDefault(import('..\\pages\\login.vue' /* webpackChunkName: "pages/login" */))
const _e8d10df0 = () => interopDefault(import('..\\pages\\logistics.vue' /* webpackChunkName: "pages/logistics" */))
const _8558d40a = () => interopDefault(import('..\\pages\\logistics_delivery.vue' /* webpackChunkName: "pages/logistics_delivery" */))
const _5e68921d = () => interopDefault(import('..\\pages\\merchant_settled.vue' /* webpackChunkName: "pages/merchant_settled" */))
const _3bd035ff = () => interopDefault(import('..\\pages\\news_list.vue' /* webpackChunkName: "pages/news_list" */))
const _18f09144 = () => interopDefault(import('..\\pages\\order_confirm.vue' /* webpackChunkName: "pages/order_confirm" */))
const _7b619606 = () => interopDefault(import('..\\pages\\order_detail.vue' /* webpackChunkName: "pages/order_detail" */))
const _6bac6c0a = () => interopDefault(import('..\\pages\\order_stay_detail.vue' /* webpackChunkName: "pages/order_stay_detail" */))
const _5a7172bb = () => interopDefault(import('..\\pages\\payment.vue' /* webpackChunkName: "pages/payment" */))
const _23e81808 = () => interopDefault(import('..\\pages\\privacy_agreement.vue' /* webpackChunkName: "pages/privacy_agreement" */))
const _3378f53f = () => interopDefault(import('..\\pages\\qualifications.vue' /* webpackChunkName: "pages/qualifications" */))
const _7e48055a = () => interopDefault(import('..\\pages\\refund.vue' /* webpackChunkName: "pages/refund" */))
const _313af754 = () => interopDefault(import('..\\pages\\refund_confirm.vue' /* webpackChunkName: "pages/refund_confirm" */))
const _79d06626 = () => interopDefault(import('..\\pages\\refund_detail.vue' /* webpackChunkName: "pages/refund_detail" */))
const _f6ba882c = () => interopDefault(import('..\\pages\\refund_goods.vue' /* webpackChunkName: "pages/refund_goods" */))
const _1c8d7a27 = () => interopDefault(import('..\\pages\\refund_logistics.vue' /* webpackChunkName: "pages/refund_logistics" */))
const _50505b01 = () => interopDefault(import('..\\pages\\reservation.vue' /* webpackChunkName: "pages/reservation" */))
const _5cab72dc = () => interopDefault(import('..\\pages\\reservation_info.vue' /* webpackChunkName: "pages/reservation_info" */))
const _1a30b473 = () => interopDefault(import('..\\pages\\shop_more.vue' /* webpackChunkName: "pages/shop_more" */))
const _4d8298a1 = () => interopDefault(import('..\\pages\\shop_street.vue' /* webpackChunkName: "pages/shop_street" */))
const _7c502be8 = () => interopDefault(import('..\\pages\\shopping_cart.vue' /* webpackChunkName: "pages/shopping_cart" */))
const _0b32f756 = () => interopDefault(import('..\\pages\\store.vue' /* webpackChunkName: "pages/store" */))
const _fa5edf28 = () => interopDefault(import('..\\pages\\store\\index.vue' /* webpackChunkName: "pages/store/index" */))
const _15757b54 = () => interopDefault(import('..\\pages\\store\\category.vue' /* webpackChunkName: "pages/store/category" */))
const _63525c01 = () => interopDefault(import('..\\pages\\store\\storeCoupon.vue' /* webpackChunkName: "pages/store/storeCoupon" */))
const _3fbd9ce6 = () => interopDefault(import('..\\pages\\user.vue' /* webpackChunkName: "pages/user" */))
const _e0c8ee48 = () => interopDefault(import('..\\pages\\user\\index.vue' /* webpackChunkName: "pages/user/index" */))
const _77e8816f = () => interopDefault(import('..\\pages\\user\\address_list.vue' /* webpackChunkName: "pages/user/address_list" */))
const _303a3534 = () => interopDefault(import('..\\pages\\user\\balance.vue' /* webpackChunkName: "pages/user/balance" */))
const _743400b4 = () => interopDefault(import('..\\pages\\user\\collect.vue' /* webpackChunkName: "pages/user/collect" */))
const _0c1a7e92 = () => interopDefault(import('..\\pages\\user\\integral.vue' /* webpackChunkName: "pages/user/integral" */))
const _dce02b54 = () => interopDefault(import('..\\pages\\user\\invoice_list.vue' /* webpackChunkName: "pages/user/invoice_list" */))
const _625629a3 = () => interopDefault(import('..\\pages\\user\\my_coupon.vue' /* webpackChunkName: "pages/user/my_coupon" */))
const _75ac9256 = () => interopDefault(import('..\\pages\\user\\order_list.vue' /* webpackChunkName: "pages/user/order_list" */))
const _861c47e4 = () => interopDefault(import('..\\pages\\user\\presell_order.vue' /* webpackChunkName: "pages/user/presell_order" */))
const _3eed33e2 = () => interopDefault(import('..\\pages\\user\\refund_list.vue' /* webpackChunkName: "pages/user/refund_list" */))
const _ab710da6 = () => interopDefault(import('..\\pages\\user\\refund_select.vue' /* webpackChunkName: "pages/user/refund_select" */))
const _877f0b62 = () => interopDefault(import('..\\pages\\user\\settle_record.vue' /* webpackChunkName: "pages/user/settle_record" */))
const _48fb824d = () => interopDefault(import('..\\pages\\goods_detail\\_id\\index.vue' /* webpackChunkName: "pages/goods_detail/_id/index" */))
const _0a762d52 = () => interopDefault(import('..\\pages\\goods_presell_detail\\_id\\index.vue' /* webpackChunkName: "pages/goods_presell_detail/_id/index" */))
const _55f160e4 = () => interopDefault(import('..\\pages\\goods_ranking\\_cate_id.vue' /* webpackChunkName: "pages/goods_ranking/_cate_id" */))
const _3ef7095d = () => interopDefault(import('..\\pages\\goods_seckill_detail\\_id\\index.vue' /* webpackChunkName: "pages/goods_seckill_detail/_id/index" */))
const _35a25cca = () => interopDefault(import('..\\pages\\news_detail\\_id\\index.vue' /* webpackChunkName: "pages/news_detail/_id/index" */))
const _7637c6c7 = () => interopDefault(import('..\\pages\\index.vue' /* webpackChunkName: "pages/index" */))

// TODO: remove in Nuxt 3
const emptyFn = () => {}
const originalPush = Router.prototype.push
Router.prototype.push = function push (location, onComplete = emptyFn, onAbort) {
  return originalPush.call(this, location, onComplete, onAbort)
}

Vue.use(Router)

export const routerOptions = {
  mode: 'history',
  base: decodeURI('/'),
  linkActiveClass: 'nuxt-link-active',
  linkExactActiveClass: 'nuxt-link-exact-active',
  scrollBehavior,

  routes: [{
    path: "/coupon_center",
    component: _71d74d83,
    name: "coupon_center"
  }, {
    path: "/evaluation",
    component: _68834997,
    name: "evaluation"
  }, {
    path: "/goods_cate",
    component: _55fa121a,
    name: "goods_cate"
  }, {
    path: "/goods_coupon",
    component: _5001c56c,
    name: "goods_coupon"
  }, {
    path: "/goods_list",
    component: _223decc2,
    name: "goods_list"
  }, {
    path: "/goods_presell",
    component: _769b8481,
    name: "goods_presell"
  }, {
    path: "/goods_search",
    component: _ed5fcc68,
    name: "goods_search"
  }, {
    path: "/goods_seckill",
    component: _3995863b,
    name: "goods_seckill"
  }, {
    path: "/login",
    component: _3fdf69de,
    name: "login"
  }, {
    path: "/logistics",
    component: _e8d10df0,
    name: "logistics"
  }, {
    path: "/logistics_delivery",
    component: _8558d40a,
    name: "logistics_delivery"
  }, {
    path: "/merchant_settled",
    component: _5e68921d,
    name: "merchant_settled"
  }, {
    path: "/news_list",
    component: _3bd035ff,
    name: "news_list"
  }, {
    path: "/order_confirm",
    component: _18f09144,
    name: "order_confirm"
  }, {
    path: "/order_detail",
    component: _7b619606,
    name: "order_detail"
  }, {
    path: "/order_stay_detail",
    component: _6bac6c0a,
    name: "order_stay_detail"
  }, {
    path: "/payment",
    component: _5a7172bb,
    name: "payment"
  }, {
    path: "/privacy_agreement",
    component: _23e81808,
    name: "privacy_agreement"
  }, {
    path: "/qualifications",
    component: _3378f53f,
    name: "qualifications"
  }, {
    path: "/refund",
    component: _7e48055a,
    name: "refund"
  }, {
    path: "/refund_confirm",
    component: _313af754,
    name: "refund_confirm"
  }, {
    path: "/refund_detail",
    component: _79d06626,
    name: "refund_detail"
  }, {
    path: "/refund_goods",
    component: _f6ba882c,
    name: "refund_goods"
  }, {
    path: "/refund_logistics",
    component: _1c8d7a27,
    name: "refund_logistics"
  }, {
    path: "/reservation",
    component: _50505b01,
    name: "reservation"
  }, {
    path: "/reservation_info",
    component: _5cab72dc,
    name: "reservation_info"
  }, {
    path: "/shop_more",
    component: _1a30b473,
    name: "shop_more"
  }, {
    path: "/shop_street",
    component: _4d8298a1,
    name: "shop_street"
  }, {
    path: "/shopping_cart",
    component: _7c502be8,
    name: "shopping_cart"
  }, {
    path: "/store",
    component: _0b32f756,
    children: [{
      path: "",
      component: _fa5edf28,
      name: "store"
    }, {
      path: "category",
      component: _15757b54,
      name: "store-category"
    }, {
      path: "storeCoupon",
      component: _63525c01,
      name: "store-storeCoupon"
    }]
  }, {
    path: "/user",
    component: _3fbd9ce6,
    children: [{
      path: "",
      component: _e0c8ee48,
      name: "user"
    }, {
      path: "address_list",
      component: _77e8816f,
      name: "user-address_list"
    }, {
      path: "balance",
      component: _303a3534,
      name: "user-balance"
    }, {
      path: "collect",
      component: _743400b4,
      name: "user-collect"
    }, {
      path: "integral",
      component: _0c1a7e92,
      name: "user-integral"
    }, {
      path: "invoice_list",
      component: _dce02b54,
      name: "user-invoice_list"
    }, {
      path: "my_coupon",
      component: _625629a3,
      name: "user-my_coupon"
    }, {
      path: "order_list",
      component: _75ac9256,
      name: "user-order_list"
    }, {
      path: "presell_order",
      component: _861c47e4,
      name: "user-presell_order"
    }, {
      path: "refund_list",
      component: _3eed33e2,
      name: "user-refund_list"
    }, {
      path: "refund_select",
      component: _ab710da6,
      name: "user-refund_select"
    }, {
      path: "settle_record",
      component: _877f0b62,
      name: "user-settle_record"
    }]
  }, {
    path: "/goods_detail/:id",
    component: _48fb824d,
    name: "goods_detail-id"
  }, {
    path: "/goods_presell_detail/:id",
    component: _0a762d52,
    name: "goods_presell_detail-id"
  }, {
    path: "/goods_ranking/:cate_id?",
    component: _55f160e4,
    name: "goods_ranking-cate_id"
  }, {
    path: "/goods_seckill_detail/:id",
    component: _3ef7095d,
    name: "goods_seckill_detail-id"
  }, {
    path: "/news_detail/:id",
    component: _35a25cca,
    name: "news_detail-id"
  }, {
    path: "/",
    component: _7637c6c7,
    name: "index"
  }],

  fallback: false
}

export function createRouter () {
  return new Router(routerOptions)
}
