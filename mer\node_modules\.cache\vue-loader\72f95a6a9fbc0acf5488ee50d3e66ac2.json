{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\components\\productSpecs.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\components\\productSpecs.vue", "mtime": 1750415772432}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\n// import leaveuKeyTerms from '@/config/leaveuKeyTerms.js';\r\n\r\nexport default {\r\n  name: 'ProductSpecs',\r\n  props: {\r\n    formValidate: {\r\n      type: Object,\r\n      default: () => ({\r\n        spec_type: 0\r\n      })\r\n    },\r\n    ManyAttrValue: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    changeAttrValue: {\r\n      type: String,\r\n      default: () => \"\"\r\n    },\r\n    attrValue: {\r\n      type: Object,\r\n      default: () => {}\r\n    },\r\n    formThead: {\r\n      type: Object,\r\n      default: () => {}\r\n    },\r\n    oneFormBatch: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    OneattrValue: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    formDynamic: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    product_id: {\r\n      type: String,\r\n      default: \"\"\r\n    },\r\n    attrs: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    cdkeyLibraryList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    selectedLibrary: {\r\n      type: Array,\r\n      default: () => []\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      ruleList: [],\r\n      showAdvancedPackages: false,\r\n      packageConfig: {\r\n        basic: {\r\n          enabled: true,\r\n          name: '基础包',\r\n          description: '',\r\n          deliveryTime: '7',\r\n          revisions: '1',\r\n          price: 0\r\n        },\r\n        standard: {\r\n          enabled: false,\r\n          name: '标准包',\r\n          description: '',\r\n          deliveryTime: '5',\r\n          revisions: '3',\r\n          price: 0\r\n        },\r\n        premium: {\r\n          enabled: false,\r\n          name: '高级包',\r\n          description: '',\r\n          deliveryTime: '3',\r\n          revisions: 'unlimited',\r\n          price: 0\r\n        }\r\n      },\r\n      extraServices: {\r\n        fastDelivery: {\r\n          enabled: false,\r\n          deliveryTime: '1',\r\n          price: 0\r\n        },\r\n        extraRevisions: {\r\n          enabled: false,\r\n          revisions: '2',\r\n          price: 0\r\n        }\r\n      }\r\n    };\r\n  },\r\n  watch: {\r\n    'formValidate.spec_type': {\r\n      handler(newVal) {\r\n        if (newVal === 2) {\r\n          // 当切换到服务包模式时，自动生成规格数据\r\n          this.generateServicePackageSpecs();\r\n        }\r\n      },\r\n      immediate: true\r\n    },\r\n    packageConfig: {\r\n      handler() {\r\n        if (this.formValidate.spec_type === 2) {\r\n          this.generateServicePackageSpecs();\r\n        }\r\n      },\r\n      deep: true\r\n    },\r\n    extraServices: {\r\n      handler() {\r\n        if (this.formValidate.spec_type === 2) {\r\n          this.generateServicePackageSpecs();\r\n        }\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  mounted() {\r\n    this.productGetRule();\r\n  },\r\n  methods: {\r\n    // 获取商品属性模板\r\n    productGetRule() {\r\n      // 这里应该调用API获取规格模板列表\r\n      // templateLsitApi().then(res => {\r\n      //   this.ruleList = res.data;\r\n      // });\r\n      this.ruleList = [];\r\n    },\r\n\r\n    // 包配置切换\r\n    onPackageToggle(packageType) {\r\n      if (packageType === 'basic' && !this.packageConfig.basic.enabled) {\r\n        this.showAdvancedPackages = false;\r\n        this.packageConfig.standard.enabled = false;\r\n        this.packageConfig.premium.enabled = false;\r\n      }\r\n    },\r\n\r\n    // 创建高级包\r\n    createAdvancedPackages() {\r\n      this.showAdvancedPackages = true;\r\n      this.packageConfig.standard.enabled = true;\r\n      this.packageConfig.premium.enabled = true;\r\n    },\r\n\r\n    // 生成服务包规格数据\r\n    generateServicePackageSpecs() {\r\n      if (this.formValidate.spec_type !== 2) return;\r\n\r\n      const attrs = [];\r\n\r\n      // 生成Package规格\r\n      const packages = [];\r\n      if (this.packageConfig.basic.enabled) packages.push('Basic');\r\n      if (this.packageConfig.standard.enabled) packages.push('Standard');\r\n      if (this.packageConfig.premium.enabled) packages.push('Premium');\r\n\r\n      if (packages.length > 0) {\r\n        attrs.push({\r\n          value: 'Package',\r\n          detail: packages.map(pkg => ({ value: pkg, pic: '' })),\r\n          add_pic: 0\r\n        });\r\n      }\r\n\r\n      // 生成快速交付规格\r\n      if (this.extraServices.fastDelivery.enabled) {\r\n        attrs.push({\r\n          value: 'Fast Delivery',\r\n          detail: [\r\n            { value: 'No', pic: '' },\r\n            { value: 'Yes', pic: '' }\r\n          ],\r\n          add_pic: 0\r\n        });\r\n      }\r\n\r\n      // 生成额外修改规格\r\n      if (this.extraServices.extraRevisions.enabled) {\r\n        attrs.push({\r\n          value: 'Extra Revisions',\r\n          detail: [\r\n            { value: 'No', pic: '' },\r\n            { value: 'Yes', pic: '' }\r\n          ],\r\n          add_pic: 0\r\n        });\r\n      }\r\n\r\n      // 触发父组件更新规格数据\r\n      this.$emit('setAttrs', attrs);\r\n    },\r\n\r\n    // 生成规格组合\r\n    generateAttrCombinations(attrs) {\r\n      if (attrs.length === 0) return [];\r\n\r\n      const combinations = [];\r\n      const generateCombination = (index, current) => {\r\n        if (index === attrs.length) {\r\n          combinations.push([...current]);\r\n          return;\r\n        }\r\n\r\n        for (const detail of attrs[index].detail) {\r\n          current.push(detail.value);\r\n          generateCombination(index + 1, current);\r\n          current.pop();\r\n        }\r\n      };\r\n\r\n      generateCombination(0, []);\r\n\r\n      // 为每个组合生成价格和库存信息，符合现有系统的数据格式\r\n      const attrValues = combinations.map((combination, index) => {\r\n        const price = this.calculateCombinationPrice(combination);\r\n        const row = {\r\n          attr_arr: combination,\r\n          detail: {},\r\n          cdkey: {},\r\n          title: \"\",\r\n          key: \"\",\r\n          price: price,\r\n          image: \"\",\r\n          ot_price: price * 1.2, // 划线价为售价的120%\r\n          cost: price * 0.7, // 成本价为售价的70%\r\n          stock: 999,\r\n          is_show: 1,\r\n          is_default_select: index === 0 ? 1 : 0, // 第一个组合默认选中\r\n          unique: \"\",\r\n          weight: 0,\r\n          volume: 0,\r\n          extension_one: 0,\r\n          extension_two: 0,\r\n          svip_price: price * 0.9, // 会员价为售价的90%\r\n          bar_code: '',\r\n          bar_code_number: ''\r\n        };\r\n\r\n        // 构建detail对象\r\n        for (let i = 0; i < combination.length; i++) {\r\n          const value = combination[i];\r\n          row.detail[attrs[i].value] = value;\r\n          row.title = attrs[i].value;\r\n          row.key = attrs[i].value;\r\n        }\r\n\r\n        return row;\r\n      });\r\n\r\n      // 不直接emit，而是让父组件的generateAttr方法处理\r\n      return attrValues;\r\n    },\r\n\r\n    // 计算组合价格\r\n    calculateCombinationPrice(combination) {\r\n      let basePrice = 0;\r\n\r\n      // 根据包类型确定基础价格\r\n      if (combination.includes('Basic')) {\r\n        basePrice = this.packageConfig.basic.price;\r\n      } else if (combination.includes('Standard')) {\r\n        basePrice = this.packageConfig.standard.price;\r\n      } else if (combination.includes('Premium')) {\r\n        basePrice = this.packageConfig.premium.price;\r\n      }\r\n\r\n      // 添加额外服务费用\r\n      if (combination.includes('Yes')) {\r\n        if (combination.some(item => item === 'Yes') && this.extraServices.fastDelivery.enabled) {\r\n          basePrice += this.extraServices.fastDelivery.price;\r\n        }\r\n        if (combination.some(item => item === 'Yes') && this.extraServices.extraRevisions.enabled) {\r\n          basePrice += this.extraServices.extraRevisions.price;\r\n        }\r\n      }\r\n\r\n      return basePrice;\r\n    },\r\n\r\n    // 选择规格模板\r\n    confirm(templateId) {\r\n      // 这里应该调用API获取模板详情并应用\r\n      console.log('Selected template:', templateId);\r\n    }\r\n  }\r\n};\r\n", null]}